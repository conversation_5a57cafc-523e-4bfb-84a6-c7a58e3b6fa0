package com.xl.alm.job.common.util;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class XirrCalculator {
    public static final BigDecimal TOL = new BigDecimal("0.001");
    private static final MathContext MC = new MathContext(15, RoundingMode.HALF_UP);
//    private static final BigDecimal DAYS_IN_YEAR = new BigDecimal("365.0006576");
    private static final BigDecimal DAYS_IN_YEAR = new BigDecimal("365.0");

    public static BigDecimal dateDiff(Date d1, Date d2) {
        long day = 24 * 60 * 60 * 1000;
        long diff = d1.getTime() - d2.getTime();
        return new BigDecimal(diff).divide(new BigDecimal(day), MC);
    }

    public static BigDecimal f_xirr(BigDecimal p, Date dt, Date dt0, BigDecimal x) {
        BigDecimal onePlusX = BigDecimal.ONE.add(x);
        BigDecimal dayRatio = dateDiff(dt0, dt).divide(DAYS_IN_YEAR, MC);
        return p.multiply(pow(onePlusX, dayRatio, MC));
    }

    // 重载方法，支持double类型参数
    public static BigDecimal f_xirr(double p, Date dt, Date dt0, double x) {
        return f_xirr(new BigDecimal(p), dt, dt0, new BigDecimal(x));
    }

    public static BigDecimal df_xirr(BigDecimal p, Date dt, Date dt0, BigDecimal x) {
        BigDecimal dayRatio = dateDiff(dt0, dt).divide(DAYS_IN_YEAR, MC);
        BigDecimal onePlusX = BigDecimal.ONE.add(x);
        BigDecimal exponent = dayRatio.subtract(BigDecimal.ONE);

        return BigDecimal.ONE.divide(DAYS_IN_YEAR, MC)
                .multiply(dateDiff(dt0, dt))
                .multiply(p)
                .multiply(pow(onePlusX, exponent, MC));
    }

    // 重载方法，支持double类型参数
    public static BigDecimal df_xirr(double p, Date dt, Date dt0, double x) {
        return df_xirr(new BigDecimal(p), dt, dt0, new BigDecimal(x));
    }

    public static BigDecimal total_f_xirr(double[] payments, Date[] days, BigDecimal x) {
        BigDecimal resf = BigDecimal.ZERO;

        for (int i = 0; i < payments.length; i++) {
            resf = resf.add(f_xirr(new BigDecimal(payments[i]), days[i], days[0], x));
        }

        return resf;
    }

    // 重载方法，支持double类型参数
    public static BigDecimal total_f_xirr(double[] payments, Date[] days, double x) {
        return total_f_xirr(payments, days, new BigDecimal(x));
    }

    public static BigDecimal total_df_xirr(double[] payments, Date[] days, BigDecimal x) {
        BigDecimal resf = BigDecimal.ZERO;

        for (int i = 0; i < payments.length; i++) {
            resf = resf.add(df_xirr(new BigDecimal(payments[i]), days[i], days[0], x));
        }

        return resf;
    }

    // 重载方法，支持double类型参数
    public static BigDecimal total_df_xirr(double[] payments, Date[] days, double x) {
        return total_df_xirr(payments, days, new BigDecimal(x));
    }

    public static BigDecimal Newtons_method(BigDecimal guess, double[] payments, Date[] days) {
        BigDecimal x0 = guess;
        BigDecimal x1;
        BigDecimal err = new BigDecimal("1e+100");
        int maxIterations = 100; // 防止无限循环
        int iteration = 0;

        while (err.compareTo(TOL) > 0 && iteration < maxIterations) {
            try {
                BigDecimal totalF = total_f_xirr(payments, days, x0);
                BigDecimal totalDf = total_df_xirr(payments, days, x0);

                // 检查导数是否为零或接近零
                if (totalDf.abs().compareTo(new BigDecimal("1e-15")) < 0) {
                    System.err.println("导数接近零，无法继续计算");
                    break;
                }

                x1 = x0.subtract(totalF.divide(totalDf, MC));
                err = x1.subtract(x0).abs();

                // 检查结果是否合理
                if (x1.abs().compareTo(new BigDecimal("1000")) > 0) {
                    System.err.println("结果发散，停止计算");
                    break;
                }

                x0 = x1;
                iteration++;
            } catch (Exception e) {
                System.err.println("计算过程中出错: " + e.getMessage());
                break;
            }
        }

        if (iteration >= maxIterations) {
            System.err.println("达到最大迭代次数，可能未收敛");
        }

        return x0;
    }

    // 重载方法，支持double类型参数
    public static BigDecimal Newtons_method(double guess, double[] payments, Date[] days) {
        return Newtons_method(new BigDecimal(guess), payments, days);
    }

    /**
     * 计算 base 的 exponent 次幂，支持小数指数
     * @param base 底数
     * @param exponent 指数
     * @param mc 精度控制
     * @return base^exponent
     */
    private static BigDecimal pow(BigDecimal base, BigDecimal exponent, MathContext mc) {
        // 处理特殊情况
        if (base.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("底数必须大于0");
        }

        if (exponent.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ONE;
        }

        if (base.compareTo(BigDecimal.ONE) == 0) {
            return BigDecimal.ONE;
        }

        // 如果指数是整数，直接使用 BigDecimal 的 pow 方法
        if (exponent.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
            try {
                return base.pow(exponent.intValue(), mc);
            } catch (ArithmeticException e) {
                // 如果指数太大，回退到对数方法
            }
        }

        // 对于小数指数，使用自然对数计算: base^exponent = e^(exponent * ln(base))
        try {
            double baseDouble = base.doubleValue();
            double exponentDouble = exponent.doubleValue();

            // 检查输入值的合理性
            if (baseDouble <= 0 || Double.isInfinite(baseDouble) || Double.isNaN(baseDouble)) {
                throw new IllegalArgumentException("底数值无效: " + baseDouble);
            }

            if (Double.isInfinite(exponentDouble) || Double.isNaN(exponentDouble)) {
                throw new IllegalArgumentException("指数值无效: " + exponentDouble);
            }

            double lnBase = Math.log(baseDouble);
            double expResult = exponentDouble * lnBase;

            // 检查中间结果
            if (Double.isInfinite(expResult) || Double.isNaN(expResult)) {
                // 如果结果会溢出，返回一个合理的边界值
                if (expResult > 700) { // e^700 约等于 10^304，接近 Double.MAX_VALUE
                    return new BigDecimal("1E+100"); // 返回一个大但有限的值
                } else if (expResult < -700) {
                    return new BigDecimal("1E-100"); // 返回一个小但非零的值
                }
            }

            double result = Math.exp(expResult);

            // 检查最终结果
            if (Double.isInfinite(result)) {
                return new BigDecimal("1E+100");
            } else if (Double.isNaN(result)) {
                return BigDecimal.ONE;
            } else if (result == 0.0) {
                return new BigDecimal("1E-100");
            }

            return new BigDecimal(result).round(mc);
        } catch (Exception e) {
            // 如果所有方法都失败，返回1作为默认值
            System.err.println("计算 " + base + "^" + exponent + " 时出错: " + e.getMessage());
            return BigDecimal.ONE;
        }
    }

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    public static Date strToDate(String str) {
        try {
            return sdf.parse(str);
        } catch (ParseException ex) {
            return null;
        }
    }

    /**
     * 从JSON字符串中提取日期和值数组
     * @param jsonStr JSON字符串
     * @return 包含days和payments的Map
     */
    public static Map<String, Object> extractDataFromJson(String jsonStr) {
        try {
            // 创建列表来存储数据
            List<Date> dateList = new ArrayList<>();
            List<Double> paymentList = new ArrayList<>();
            Map<Integer, String[]> dataMap = new TreeMap<>(); // 使用TreeMap自动排序

            // 使用正则表达式提取数据
            Pattern pattern = Pattern.compile("\"(\\d+)\":\\{\"date\":\"([^\"]+)\",\"value\":\"([^\"]+)\"");
            Matcher matcher = pattern.matcher(jsonStr);

            while (matcher.find()) {
                int index = Integer.parseInt(matcher.group(1));
                String dateStr = matcher.group(2);
                String valueStr = matcher.group(3);
                dataMap.put(index, new String[]{dateStr, valueStr});
            }

            // 按顺序处理数据
            for (String[] data : dataMap.values()) {
                String dateStr = data[0];
                String valueStr = data[1];

                Date date = strToDate(dateStr);
                if (date != null) {
                    dateList.add(date);
                    paymentList.add(Double.parseDouble(valueStr));
                }
            }

            // 转换为数组
            Date[] days = dateList.toArray(new Date[0]);
            double[] payments = paymentList.stream().mapToDouble(Double::doubleValue).toArray();

            Map<String, Object> result = new HashMap<>();
            result.put("days", days);
            result.put("payments", payments);

            return result;
        } catch (Exception e) {
            System.err.println("解析JSON时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        String json="{\"0\":{\"date\":\"2024-12-31\",\"value\":\"268268.8000000000\"},\"1\":{\"date\":\"2025-01-31\",\"value\":\"-7904.92317432\"},\"2\":{\"date\":\"2025-02-28\",\"value\":\"-7691.60483486\"},\"3\":{\"date\":\"2025-03-31\",\"value\":\"-7484.04301033\"},\"4\":{\"date\":\"2025-04-30\",\"value\":\"-7282.08235763\"},\"5\":{\"date\":\"2025-05-31\",\"value\":\"-7085.5717257\"},\"6\":{\"date\":\"2025-06-30\",\"value\":\"-6894.36404238\"},\"7\":{\"date\":\"2025-07-31\",\"value\":\"-6708.31620436\"},\"8\":{\"date\":\"2025-08-31\",\"value\":\"-6527.28897006\"},\"9\":{\"date\":\"2025-09-30\",\"value\":\"-6352.95458003\"},\"10\":{\"date\":\"2025-10-31\",\"value\":\"-6181.46363653\"},\"11\":{\"date\":\"2025-11-30\",\"value\":\"-6014.60191148\"},\"12\":{\"date\":\"2025-12-31\",\"value\":\"-5852.24444365\"},\"13\":{\"date\":\"2026-01-31\",\"value\":\"-5694.26964507\"},\"14\":{\"date\":\"2026-02-28\",\"value\":\"-5540.55920989\"},\"15\":{\"date\":\"2026-03-31\",\"value\":\"-5390.99802584\"},\"16\":{\"date\":\"2026-04-30\",\"value\":\"-5245.47408799\"},\"17\":{\"date\":\"2026-05-31\",\"value\":\"-5103.87841488\"},\"18\":{\"date\":\"2026-06-30\",\"value\":\"-4966.1049669\"},\"19\":{\"date\":\"2026-07-31\",\"value\":\"-4832.05056689\"},\"20\":{\"date\":\"2026-08-31\",\"value\":\"-4701.61482285\"},\"21\":{\"date\":\"2026-09-30\",\"value\":\"-4576.09365645\"},\"22\":{\"date\":\"2026-10-31\",\"value\":\"-4452.52606025\"},\"23\":{\"date\":\"2026-11-30\",\"value\":\"-4332.29515135\"},\"24\":{\"date\":\"2026-12-31\",\"value\":\"-4215.31082916\"},\"25\":{\"date\":\"2027-01-31\",\"value\":\"-4101.48542609\"},\"26\":{\"date\":\"2027-02-28\",\"value\":\"-3990.73364183\"},\"27\":{\"date\":\"2027-03-31\",\"value\":\"-3882.97247946\"},\"28\":{\"date\":\"2027-04-30\",\"value\":\"-3778.12118322\"},\"29\":{\"date\":\"2027-05-31\",\"value\":\"-3676.10117799\"},\"30\":{\"date\":\"2027-06-30\",\"value\":\"-3576.83601045\"},\"31\":{\"date\":\"2027-07-31\",\"value\":\"-3480.25129174\"},\"32\":{\"date\":\"2027-08-31\",\"value\":\"-3386.27464172\"},\"33\":{\"date\":\"2027-09-30\",\"value\":\"-3295.92508036\"},\"34\":{\"date\":\"2027-10-31\",\"value\":\"-3206.89360324\"},\"35\":{\"date\":\"2027-11-30\",\"value\":\"-3120.26710486\"},\"36\":{\"date\":\"2027-12-31\",\"value\":\"-3035.98062009\"},\"37\":{\"date\":\"2028-01-31\",\"value\":\"-2953.9709387\"},\"38\":{\"date\":\"2028-02-29\",\"value\":\"-2874.17655794\"},\"39\":{\"date\":\"2028-03-31\",\"value\":\"-2796.53763642\"},\"40\":{\"date\":\"2028-04-30\",\"value\":\"-2720.99594924\"},\"41\":{\"date\":\"2028-05-31\",\"value\":\"-2647.49484433\"},\"42\":{\"date\":\"2028-06-30\",\"value\":\"-2575.97919994\"},\"43\":{\"date\":\"2028-07-31\",\"value\":\"-2506.39538332\"},\"44\":{\"date\":\"2028-08-31\",\"value\":\"-2438.6912105\"},\"45\":{\"date\":\"2028-09-30\",\"value\":\"-2373.66201197\"},\"46\":{\"date\":\"2028-10-31\",\"value\":\"-2309.5183235\"},\"47\":{\"date\":\"2028-11-30\",\"value\":\"-2247.10800245\"},\"48\":{\"date\":\"2028-12-31\",\"value\":\"-2186.38420751\"},\"49\":{\"date\":\"2029-01-31\",\"value\":\"-2127.30136319\"},\"50\":{\"date\":\"2029-02-28\",\"value\":\"-2069.81512559\"},\"51\":{\"date\":\"2029-03-31\",\"value\":\"-2013.88234915\"},\"52\":{\"date\":\"2029-04-30\",\"value\":\"-1959.46105425\"},\"53\":{\"date\":\"2029-05-31\",\"value\":\"-1906.5103957\"},\"54\":{\"date\":\"2029-06-30\",\"value\":\"-1854.99063207\"},\"55\":{\"date\":\"2029-07-31\",\"value\":\"-1804.86309589\"},\"56\":{\"date\":\"2029-08-31\",\"value\":\"-1756.09016462\"},\"57\":{\"date\":\"2029-09-30\",\"value\":\"-1709.29302106\"},\"58\":{\"date\":\"2029-10-31\",\"value\":\"-1663.08325378\"},\"59\":{\"date\":\"2029-11-30\",\"value\":\"-1618.12274685\"},\"60\":{\"date\":\"2029-12-31\",\"value\":\"-1574.37772694\"},\"61\":{\"date\":\"2030-01-31\",\"value\":\"-1531.81533376\"},\"62\":{\"date\":\"2030-02-28\",\"value\":\"-1490.40359538\"},\"63\":{\"date\":\"2030-03-31\",\"value\":\"-1450.11140424\"},\"64\":{\"date\":\"2030-04-30\",\"value\":\"-1410.90849377\"},\"65\":{\"date\":\"2030-05-31\",\"value\":\"-1372.76541564\"},\"66\":{\"date\":\"2030-06-30\",\"value\":\"-1335.65351766\"},\"67\":{\"date\":\"2030-07-31\",\"value\":\"-1299.54492223\"},\"68\":{\"date\":\"2030-08-31\",\"value\":\"-1264.41250542\"},\"69\":{\"date\":\"2030-09-30\",\"value\":\"-1230.74134233\"},\"70\":{\"date\":\"2030-10-31\",\"value\":\"-1197.45387246\"},\"71\":{\"date\":\"2030-11-30\",\"value\":\"-1165.0667228\"},\"72\":{\"date\":\"2030-12-31\",\"value\":\"-1133.55554245\"},\"73\":{\"date\":\"2031-01-31\",\"value\":\"-1102.89663909\"},\"74\":{\"date\":\"2031-02-28\",\"value\":\"-1073.06696123\"},\"75\":{\"date\":\"2031-03-31\",\"value\":\"-1044.04408085\"},\"76\":{\"date\":\"2031-04-30\",\"value\":\"-1015.80617655\"},\"77\":{\"date\":\"2031-05-31\",\"value\":\"-988.33201713\"},\"78\":{\"date\":\"2031-06-30\",\"value\":\"-961.60094563\"},\"79\":{\"date\":\"2031-07-31\",\"value\":\"-935.59286379\"},\"80\":{\"date\":\"2031-08-31\",\"value\":\"-910.28821698\"},\"81\":{\"date\":\"2031-09-30\",\"value\":\"-886.06336895\"},\"82\":{\"date\":\"2031-10-31\",\"value\":\"-862.08663711\"},\"83\":{\"date\":\"2031-11-30\",\"value\":\"-838.75871561\"},\"84\":{\"date\":\"2031-12-31\",\"value\":\"-816.06204753\"},\"85\":{\"date\":\"2032-01-31\",\"value\":\"-793.97955107\"},\"86\":{\"date\":\"2032-02-29\",\"value\":\"-772.49460665\"},\"87\":{\"date\":\"2032-03-31\",\"value\":\"-751.59104442\"},\"88\":{\"date\":\"2032-04-30\",\"value\":\"-731.25313209\"},\"89\":{\"date\":\"2032-05-31\",\"value\":\"-711.46556311\"},\"90\":{\"date\":\"2032-06-30\",\"value\":\"-692.2134451\"},\"91\":{\"date\":\"2032-07-31\",\"value\":\"-673.4822887\"},\"92\":{\"date\":\"2032-08-31\",\"value\":\"-655.25799662\"},\"93\":{\"date\":\"2032-09-30\",\"value\":\"-637.83334639\"},\"94\":{\"date\":\"2032-10-31\",\"value\":\"-620.56463979\"},\"95\":{\"date\":\"2032-11-30\",\"value\":\"-603.76346953\"},\"96\":{\"date\":\"2032-12-31\",\"value\":\"-587.41717735\"},\"97\":{\"date\":\"2033-01-31\",\"value\":\"-571.51344771\"},\"98\":{\"date\":\"2033-02-28\",\"value\":\"-556.04029851\"},\"99\":{\"date\":\"2033-03-31\",\"value\":\"-540.98607205\"},\"100\":{\"date\":\"2033-04-30\",\"value\":\"-526.33942628\"},\"101\":{\"date\":\"2033-05-31\",\"value\":\"-512.0893262\"},\"102\":{\"date\":\"2033-06-30\",\"value\":\"-498.2250356\"},\"103\":{\"date\":\"2033-07-31\",\"value\":\"-484.73610894\"},\"104\":{\"date\":\"2033-08-31\",\"value\":\"-471.61238349\"},\"105\":{\"date\":\"2033-09-30\",\"value\":\"-459.08140528\"},\"106\":{\"date\":\"2033-10-31\",\"value\":\"-446.64522598\"},\"107\":{\"date\":\"2033-11-30\",\"value\":\"-434.54593633\"},\"108\":{\"date\":\"2033-12-31\",\"value\":\"-422.77441009\"},\"109\":{\"date\":\"2034-01-31\",\"value\":\"-411.32176824\"},\"110\":{\"date\":\"2034-02-28\",\"value\":\"-400.17937232\"},\"111\":{\"date\":\"2034-03-31\",\"value\":\"-389.33881786\"},\"112\":{\"date\":\"2034-04-30\",\"value\":\"-378.79192807\"},\"113\":{\"date\":\"2034-05-31\",\"value\":\"-368.53074768\"},\"114\":{\"date\":\"2034-06-30\",\"value\":\"-358.54753692\"},\"115\":{\"date\":\"2034-07-31\",\"value\":\"-348.83476568\"},\"116\":{\"date\":\"2034-08-31\",\"value\":\"-339.38510786\"},\"117\":{\"date\":\"2034-09-30\",\"value\":\"-330.37439034\"},\"118\":{\"date\":\"2034-10-31\",\"value\":\"-321.4193906\"},\"119\":{\"date\":\"2034-11-30\",\"value\":\"-312.70712379\"},\"120\":{\"date\":\"2034-12-31\",\"value\":\"-304.23101038\"},\"121\":{\"date\":\"2035-01-31\",\"value\":\"-295.98464916\"},\"122\":{\"date\":\"2035-02-28\",\"value\":\"-287.96181245\"},\"123\":{\"date\":\"2035-03-31\",\"value\":\"-280.15644138\"},\"124\":{\"date\":\"2035-04-30\",\"value\":\"-272.5626413\"},\"125\":{\"date\":\"2035-05-31\",\"value\":\"-265.17467737\"},\"126\":{\"date\":\"2035-06-30\",\"value\":\"-257.98697017\"},\"127\":{\"date\":\"2035-07-31\",\"value\":\"-250.99409153\"},\"128\":{\"date\":\"2035-08-31\",\"value\":\"-244.19076043\"},\"129\":{\"date\":\"2035-09-30\",\"value\":\"-237.71302806\"},\"130\":{\"date\":\"2035-10-31\",\"value\":\"-231.26550303\"},\"131\":{\"date\":\"2035-11-30\",\"value\":\"-224.99285697\"},\"132\":{\"date\":\"2035-12-31\",\"value\":\"-218.89034651\"},\"133\":{\"date\":\"2036-01-31\",\"value\":\"-212.95335694\"},\"134\":{\"date\":\"2036-02-29\",\"value\":\"-207.17739873\"},\"135\":{\"date\":\"2036-03-31\",\"value\":\"-201.55810412\"},\"136\":{\"date\":\"2036-04-30\",\"value\":\"-196.0912238\"},\"137\":{\"date\":\"2036-05-31\",\"value\":\"-190.77262375\"},\"138\":{\"date\":\"2036-06-30\",\"value\":\"-185.59828206\"},\"139\":{\"date\":\"2036-07-31\",\"value\":\"-180.56428593\"},\"140\":{\"date\":\"2036-08-31\",\"value\":\"-175.66682865\"},\"141\":{\"date\":\"2036-09-30\",\"value\":\"-171.01190041\"},\"142\":{\"date\":\"2036-10-31\",\"value\":\"-166.37027997\"},\"143\":{\"date\":\"2036-11-30\",\"value\":\"-161.85464431\"},\"144\":{\"date\":\"2036-12-31\",\"value\":\"-157.46157385\"},\"145\":{\"date\":\"2037-01-31\",\"value\":\"-153.18774183\"},\"146\":{\"date\":\"2037-02-28\",\"value\":\"-149.0299118\"},\"147\":{\"date\":\"2037-03-31\",\"value\":\"-144.98493515\"},\"148\":{\"date\":\"2037-04-30\",\"value\":\"-141.04974874\"},\"149\":{\"date\":\"2037-05-31\",\"value\":\"-137.22137256\"},\"150\":{\"date\":\"2037-06-30\",\"value\":\"-133.49690751\"},\"151\":{\"date\":\"2037-07-31\",\"value\":\"-129.87353315\"},\"152\":{\"date\":\"2037-08-31\",\"value\":\"-126.34850561\"},\"153\":{\"date\":\"2037-09-30\",\"value\":\"-123.00375005\"},\"154\":{\"date\":\"2037-10-31\",\"value\":\"-119.66267151\"},\"155\":{\"date\":\"2037-11-30\",\"value\":\"-116.41234595\"},\"156\":{\"date\":\"2037-12-31\",\"value\":\"-113.25030822\"},\"157\":{\"date\":\"2038-01-31\",\"value\":\"-110.17416016\"},\"158\":{\"date\":\"2038-02-28\",\"value\":\"-107.18156874\"},\"159\":{\"date\":\"2038-03-31\",\"value\":\"-104.27026429\"},\"160\":{\"date\":\"2038-04-30\",\"value\":\"-101.43803881\"},\"161\":{\"date\":\"2038-05-31\",\"value\":\"-98.68274427\"},\"162\":{\"date\":\"2038-06-30\",\"value\":\"-96.00229098\"},\"163\":{\"date\":\"2038-07-31\",\"value\":\"-93.39464604\"},\"164\":{\"date\":\"2038-08-31\",\"value\":\"-90.85783174\"},\"165\":{\"date\":\"2038-09-30\",\"value\":\"-88.45528591\"},\"166\":{\"date\":\"2038-10-31\",\"value\":\"-86.05069461\"},\"167\":{\"date\":\"2038-11-30\",\"value\":\"-83.71147134\"},\"168\":{\"date\":\"2038-12-31\",\"value\":\"-81.43583908\"},\"169\":{\"date\":\"2039-01-31\",\"value\":\"-79.22206909\"},\"170\":{\"date\":\"2039-02-28\",\"value\":\"-77.06847965\"},\"171\":{\"date\":\"2039-03-31\",\"value\":\"-74.97343473\"},\"172\":{\"date\":\"2039-04-30\",\"value\":\"-72.93534282\"},\"173\":{\"date\":\"2039-05-31\",\"value\":\"-70.95265563\"},\"174\":{\"date\":\"2039-06-30\",\"value\":\"-69.02386699\"},\"175\":{\"date\":\"2039-07-31\",\"value\":\"-67.14751166\"},\"176\":{\"date\":\"2039-08-31\",\"value\":\"-65.32216425\"},\"177\":{\"date\":\"2039-09-30\",\"value\":\"-63.59691182\"},\"178\":{\"date\":\"2039-10-31\",\"value\":\"-61.86658392\"},\"179\":{\"date\":\"2039-11-30\",\"value\":\"-60.18333515\"},\"180\":{\"date\":\"2039-12-31\",\"value\":\"-58.54588455\"},\"181\":{\"date\":\"2040-01-31\",\"value\":\"-56.95298601\"},\"182\":{\"date\":\"2040-02-29\",\"value\":\"-55.40342733\"},\"183\":{\"date\":\"2040-03-31\",\"value\":\"-53.89602929\"},\"184\":{\"date\":\"2040-04-30\",\"value\":\"-52.42964475\"},\"185\":{\"date\":\"2040-05-31\",\"value\":\"-51.00315781\"},\"186\":{\"date\":\"2040-06-30\",\"value\":\"-49.61548289\"},\"187\":{\"date\":\"2040-07-31\",\"value\":\"-48.26556397\"},\"188\":{\"date\":\"2040-08-31\",\"value\":\"-46.95237378\"},\"189\":{\"date\":\"2040-09-30\",\"value\":\"-45.71419849\"},\"190\":{\"date\":\"2040-10-31\",\"value\":\"-44.46925586\"},\"191\":{\"date\":\"2040-11-30\",\"value\":\"-43.25821764\"},\"192\":{\"date\":\"2040-12-31\",\"value\":\"-42.08016048\"},\"193\":{\"date\":\"2041-01-31\",\"value\":\"-40.93418616\"},\"194\":{\"date\":\"2041-02-28\",\"value\":\"-39.81942094\"},\"195\":{\"date\":\"2041-03-31\",\"value\":\"-38.73501484\"},\"196\":{\"date\":\"2041-04-30\",\"value\":\"-37.68014108\"},\"197\":{\"date\":\"2041-05-31\",\"value\":\"-36.65399535\"},\"198\":{\"date\":\"2041-06-30\",\"value\":\"-35.65579528\"},\"199\":{\"date\":\"2041-07-31\",\"value\":\"-34.68477978\"},\"200\":{\"date\":\"2041-08-31\",\"value\":\"-33.74020852\"},\"201\":{\"date\":\"2041-09-30\",\"value\":\"-32.85210088\"},\"202\":{\"date\":\"2041-10-31\",\"value\":\"-31.95652349\"},\"203\":{\"date\":\"2041-11-30\",\"value\":\"-31.08536088\"},\"204\":{\"date\":\"2041-12-31\",\"value\":\"-30.23794747\"},\"205\":{\"date\":\"2042-01-31\",\"value\":\"-29.41363579\"},\"206\":{\"date\":\"2042-02-28\",\"value\":\"-28.61179605\"},\"207\":{\"date\":\"2042-03-31\",\"value\":\"-27.83181562\"},\"208\":{\"date\":\"2042-04-30\",\"value\":\"-27.07309856\"},\"209\":{\"date\":\"2042-05-31\",\"value\":\"-26.33506519\"},\"210\":{\"date\":\"2042-06-30\",\"value\":\"-25.61715164\"},\"211\":{\"date\":\"2042-07-31\",\"value\":\"-24.9188094\"},\"212\":{\"date\":\"2042-08-31\",\"value\":\"-24.2395049\"},\"213\":{\"date\":\"2042-09-30\",\"value\":\"-23.60294083\"},\"214\":{\"date\":\"2042-10-31\",\"value\":\"-22.9587858\"},\"215\":{\"date\":\"2042-11-30\",\"value\":\"-22.33221105\"},\"216\":{\"date\":\"2042-12-31\",\"value\":\"-21.72273676\"},\"217\":{\"date\":\"2043-01-31\",\"value\":\"-21.12989623\"},\"218\":{\"date\":\"2043-02-28\",\"value\":\"-20.55323548\"},\"219\":{\"date\":\"2043-03-31\",\"value\":\"-19.9923129\"},\"220\":{\"date\":\"2043-04-30\",\"value\":\"-19.44669898\"},\"221\":{\"date\":\"2043-05-31\",\"value\":\"-18.91597589\"},\"222\":{\"date\":\"2043-06-30\",\"value\":\"-18.39973722\"},\"223\":{\"date\":\"2043-07-31\",\"value\":\"-17.89758766\"},\"224\":{\"date\":\"2043-08-31\",\"value\":\"-17.40914268\"},\"225\":{\"date\":\"2043-09-30\",\"value\":\"-16.95322285\"},\"226\":{\"date\":\"2043-10-31\",\"value\":\"-16.48997791\"},\"227\":{\"date\":\"2043-11-30\",\"value\":\"-16.03939144\"},\"228\":{\"date\":\"2043-12-31\",\"value\":\"-15.60111755\"},\"229\":{\"date\":\"2044-01-31\",\"value\":\"-15.17481976\"},\"230\":{\"date\":\"2044-02-29\",\"value\":\"-14.76017083\"},\"231\":{\"date\":\"2044-03-31\",\"value\":\"-14.35685241\"},\"232\":{\"date\":\"2044-04-30\",\"value\":\"-13.9645549\"},\"233\":{\"date\":\"2044-05-31\",\"value\":\"-13.58297714\"},\"234\":{\"date\":\"2044-06-30\",\"value\":\"-13.21182618\"},\"235\":{\"date\":\"2044-07-31\",\"value\":\"-12.85081711\"},\"236\":{\"date\":\"2044-08-31\",\"value\":\"-12.49967278\"},\"237\":{\"date\":\"2044-09-30\",\"value\":\"-12.17344157\"},\"238\":{\"date\":\"2044-10-31\",\"value\":\"-11.84034882\"},\"239\":{\"date\":\"2044-11-30\",\"value\":\"-11.51637052\"},\"240\":{\"date\":\"2044-12-31\",\"value\":\"-11.20125726\"},\"241\":{\"date\":\"2045-01-31\",\"value\":\"-10.89476647\"},\"242\":{\"date\":\"2045-02-28\",\"value\":\"-10.5966622\"},\"243\":{\"date\":\"2045-03-31\",\"value\":\"-10.30671496\"},\"244\":{\"date\":\"2045-04-30\",\"value\":\"-10.02470155\"},\"245\":{\"date\":\"2045-05-31\",\"value\":\"-9.75040486\"},\"246\":{\"date\":\"2045-06-30\",\"value\":\"-9.48361374\"},\"247\":{\"date\":\"2045-07-31\",\"value\":\"-9.2241228\"},\"248\":{\"date\":\"2045-08-31\",\"value\":\"-8.97173228\"},\"249\":{\"date\":\"2045-09-30\",\"value\":\"-8.73855432\"},\"250\":{\"date\":\"2045-10-31\",\"value\":\"-8.49908196\"},\"251\":{\"date\":\"2045-11-30\",\"value\":\"-8.26617235\"},\"252\":{\"date\":\"2045-12-31\",\"value\":\"-8.03964564\"},\"253\":{\"date\":\"2046-01-31\",\"value\":\"-7.81932689\"},\"254\":{\"date\":\"2046-02-28\",\"value\":\"-7.60504598\"},\"255\":{\"date\":\"2046-03-31\",\"value\":\"-7.39663742\"},\"256\":{\"date\":\"2046-04-30\",\"value\":\"-7.19394028\"},\"257\":{\"date\":\"2046-05-31\",\"value\":\"-6.99679803\"},\"258\":{\"date\":\"2046-06-30\",\"value\":\"-6.80505845\"},\"259\":{\"date\":\"2046-07-31\",\"value\":\"-6.61857345\"},\"260\":{\"date\":\"2046-08-31\",\"value\":\"-6.43719905\"},\"261\":{\"date\":\"2046-09-30\",\"value\":\"-6.27078687\"},\"262\":{\"date\":\"2046-10-31\",\"value\":\"-6.09864394\"},\"263\":{\"date\":\"2046-11-30\",\"value\":\"-5.93122678\"},\"264\":{\"date\":\"2046-12-31\",\"value\":\"-5.76840564\"},\"265\":{\"date\":\"2047-01-31\",\"value\":\"-5.61005436\"},\"266\":{\"date\":\"2047-02-28\",\"value\":\"-5.45605022\"},\"267\":{\"date\":\"2047-03-31\",\"value\":\"-5.30627387\"},\"268\":{\"date\":\"2047-04-30\",\"value\":\"-5.16060925\"},\"269\":{\"date\":\"2047-05-31\",\"value\":\"-5.01894347\"},\"270\":{\"date\":\"2047-06-30\",\"value\":\"-4.88116676\"},\"271\":{\"date\":\"2047-07-31\",\"value\":\"-4.74717235\"},\"272\":{\"date\":\"2047-08-31\",\"value\":\"-4.61685639\"},\"273\":{\"date\":\"2047-09-30\",\"value\":\"-4.49831978\"},\"274\":{\"date\":\"2047-10-31\",\"value\":\"-4.37458902\"},\"275\":{\"date\":\"2047-11-30\",\"value\":\"-4.25426173\"},\"276\":{\"date\":\"2047-12-31\",\"value\":\"-4.1372443\"},\"277\":{\"date\":\"2048-01-31\",\"value\":\"-4.02344567\"},\"278\":{\"date\":\"2048-02-29\",\"value\":\"-3.91277731\"},\"279\":{\"date\":\"2048-03-31\",\"value\":\"-3.80515309\"},\"280\":{\"date\":\"2048-04-30\",\"value\":\"-3.7004893\"},\"281\":{\"date\":\"2048-05-31\",\"value\":\"-3.59870448\"},\"282\":{\"date\":\"2048-06-30\",\"value\":\"-3.49971945\"},\"283\":{\"date\":\"2048-07-31\",\"value\":\"-3.40345718\"},\"284\":{\"date\":\"2048-08-31\",\"value\":\"-3.30984279\"},\"285\":{\"date\":\"2048-09-30\",\"value\":\"-3.22564414\"},\"286\":{\"date\":\"2048-10-31\",\"value\":\"-3.13671478\"},\"287\":{\"date\":\"2048-11-30\",\"value\":\"-3.05023727\"},\"288\":{\"date\":\"2048-12-31\",\"value\":\"-2.96614401\"},\"289\":{\"date\":\"2049-01-31\",\"value\":\"-2.88436926\"},\"290\":{\"date\":\"2049-02-28\",\"value\":\"-2.80484909\"},\"291\":{\"date\":\"2049-03-31\",\"value\":\"-2.72752134\"},\"292\":{\"date\":\"2049-04-30\",\"value\":\"-2.65232557\"},\"293\":{\"date\":\"2049-05-31\",\"value\":\"-2.57920299\"},\"294\":{\"date\":\"2049-06-30\",\"value\":\"-2.50809643\"},\"295\":{\"date\":\"2049-07-31\",\"value\":\"-2.43895032\"},\"296\":{\"date\":\"2049-08-31\",\"value\":\"-2.37171059\"},\"297\":{\"date\":\"2049-09-30\",\"value\":\"-2.31213867\"},\"298\":{\"date\":\"2049-10-31\",\"value\":\"-2.24821951\"},\"299\":{\"date\":\"2049-11-30\",\"value\":\"-2.18606749\"},\"300\":{\"date\":\"2049-12-31\",\"value\":\"-2.12563376\"},\"301\":{\"date\":\"2050-01-31\",\"value\":\"-2.06687082\"},\"302\":{\"date\":\"2050-02-28\",\"value\":\"-2.00973246\"},\"303\":{\"date\":\"2050-03-31\",\"value\":\"-1.95417377\"},\"304\":{\"date\":\"2050-04-30\",\"value\":\"-1.90015107\"},\"305\":{\"date\":\"2050-05-31\",\"value\":\"-1.84762191\"},\"306\":{\"date\":\"2050-06-30\",\"value\":\"-1.79654498\"},\"307\":{\"date\":\"2050-07-31\",\"value\":\"-1.74688013\"},\"308\":{\"date\":\"2050-08-31\",\"value\":\"-1.69858833\"},\"309\":{\"date\":\"2050-09-30\",\"value\":\"-1.6566633\"},\"310\":{\"date\":\"2050-10-31\",\"value\":\"-1.610713\"},\"311\":{\"date\":\"2050-11-30\",\"value\":\"-1.5660373\"},\"312\":{\"date\":\"2050-12-31\",\"value\":\"-1.52260084\"},\"313\":{\"date\":\"2051-01-31\",\"value\":\"-1.48036924\"},\"314\":{\"date\":\"2051-02-28\",\"value\":\"-1.43930908\"},\"315\":{\"date\":\"2051-03-31\",\"value\":\"-1.39938786\"},\"316\":{\"date\":\"2051-04-30\",\"value\":\"-1.36057399\"},\"317\":{\"date\":\"2051-05-31\",\"value\":\"-1.32283675\"},\"318\":{\"date\":\"2051-06-30\",\"value\":\"-1.28614627\"},\"319\":{\"date\":\"2051-07-31\",\"value\":\"-1.25047352\"},\"320\":{\"date\":\"2051-08-31\",\"value\":\"-1.21579026\"},\"321\":{\"date\":\"2051-09-30\",\"value\":\"-1.18648613\"},\"322\":{\"date\":\"2051-10-31\",\"value\":\"-1.15344314\"},\"323\":{\"date\":\"2051-11-30\",\"value\":\"-1.12132046\"},\"324\":{\"date\":\"2051-12-31\",\"value\":\"-1.09009247\"},\"325\":{\"date\":\"2052-01-31\",\"value\":\"-1.05973423\"},\"326\":{\"date\":\"2052-02-29\",\"value\":\"-1.03022152\"},\"327\":{\"date\":\"2052-03-31\",\"value\":\"-1.00153079\"},\"328\":{\"date\":\"2052-04-30\",\"value\":\"-0.97363915\"},\"329\":{\"date\":\"2052-05-31\",\"value\":\"-0.94652433\"},\"330\":{\"date\":\"2052-06-30\",\"value\":\"-0.9201647\"},\"331\":{\"date\":\"2052-07-31\",\"value\":\"-0.89453922\"},\"332\":{\"date\":\"2052-08-31\",\"value\":\"-0.86962745\"},\"333\":{\"date\":\"2052-09-30\",\"value\":\"-0.84930877\"},\"334\":{\"date\":\"2052-10-31\",\"value\":\"-0.82553724\"},\"335\":{\"date\":\"2052-11-30\",\"value\":\"-0.80243114\"},\"336\":{\"date\":\"2052-12-31\",\"value\":\"-0.77997184\"},\"337\":{\"date\":\"2053-01-31\",\"value\":\"-0.75814124\"},\"338\":{\"date\":\"2053-02-28\",\"value\":\"-0.73692172\"},\"339\":{\"date\":\"2053-03-31\",\"value\":\"-0.71629619\"},\"340\":{\"date\":\"2053-04-30\",\"value\":\"-0.69624801\"},\"341\":{\"date\":\"2053-05-31\",\"value\":\"-0.67676102\"},\"342\":{\"date\":\"2053-06-30\",\"value\":\"-0.65781952\"},\"343\":{\"date\":\"2053-07-31\",\"value\":\"-0.63940822\"},\"344\":{\"date\":\"2053-08-31\",\"value\":\"-0.62151229\"},\"345\":{\"date\":\"2053-09-30\",\"value\":\"-0.607547\"},\"346\":{\"date\":\"2053-10-31\",\"value\":\"-0.59043715\"},\"347\":{\"date\":\"2053-11-30\",\"value\":\"-0.57380923\"},\"348\":{\"date\":\"2053-12-31\",\"value\":\"-0.55764967\"},\"349\":{\"date\":\"2054-01-31\",\"value\":\"-0.54194526\"},\"350\":{\"date\":\"2054-02-28\",\"value\":\"-0.5266832\"},\"351\":{\"date\":\"2054-03-31\",\"value\":\"-0.51185101\"},\"352\":{\"date\":\"2054-04-30\",\"value\":\"-0.49743658\"},\"353\":{\"date\":\"2054-05-31\",\"value\":\"-0.48342815\"},\"354\":{\"date\":\"2054-06-30\",\"value\":\"-0.46981429\"},\"355\":{\"date\":\"2054-07-31\",\"value\":\"-0.45658386\"},\"356\":{\"date\":\"2054-08-31\",\"value\":\"-0.44372609\"},\"357\":{\"date\":\"2054-09-30\",\"value\":\"-0.43420716\"},\"358\":{\"date\":\"2054-10-31\",\"value\":\"-0.42188714\"},\"359\":{\"date\":\"2054-11-30\",\"value\":\"-0.40991676\"},\"360\":{\"date\":\"2054-12-31\",\"value\":\"-0.39828609\"},\"361\":{\"date\":\"2055-01-31\",\"value\":\"-0.3869855\"},\"362\":{\"date\":\"2055-02-28\",\"value\":\"-0.37600562\"},\"363\":{\"date\":\"2055-03-31\",\"value\":\"-0.36533734\"},\"364\":{\"date\":\"2055-04-30\",\"value\":\"-0.35497181\"},\"365\":{\"date\":\"2055-05-31\",\"value\":\"-0.34490044\"},\"366\":{\"date\":\"2055-06-30\",\"value\":\"-0.33511489\"},\"367\":{\"date\":\"2055-07-31\",\"value\":\"-0.32560704\"},\"368\":{\"date\":\"2055-08-31\",\"value\":\"-0.316369\"},\"369\":{\"date\":\"2055-09-30\",\"value\":\"-0.30992667\"},\"370\":{\"date\":\"2055-10-31\",\"value\":\"-0.30105419\"},\"371\":{\"date\":\"2055-11-30\",\"value\":\"-0.29243578\"},\"372\":{\"date\":\"2055-12-31\",\"value\":\"-0.28406416\"},\"373\":{\"date\":\"2056-01-31\",\"value\":\"-0.27593228\"},\"374\":{\"date\":\"2056-02-29\",\"value\":\"-0.26803325\"},\"375\":{\"date\":\"2056-03-31\",\"value\":\"-0.26036042\"},\"376\":{\"date\":\"2056-04-30\",\"value\":\"-0.2529073\"},\"377\":{\"date\":\"2056-05-31\",\"value\":\"-0.2456676\"},\"378\":{\"date\":\"2056-06-30\",\"value\":\"-0.2386352\"},\"379\":{\"date\":\"2056-07-31\",\"value\":\"-0.23180418\"},\"380\":{\"date\":\"2056-08-31\",\"value\":\"-0.22516875\"},\"381\":{\"date\":\"2056-09-30\",\"value\":\"-0.22082773\"},\"382\":{\"date\":\"2056-10-31\",\"value\":\"-0.21443994\"},\"383\":{\"date\":\"2056-11-30\",\"value\":\"-0.20823701\"},\"384\":{\"date\":\"2056-12-31\",\"value\":\"-0.20221357\"},\"385\":{\"date\":\"2057-01-31\",\"value\":\"-0.19636443\"},\"386\":{\"date\":\"2057-02-28\",\"value\":\"-0.19068456\"},\"387\":{\"date\":\"2057-03-31\",\"value\":\"-0.18516904\"},\"388\":{\"date\":\"2057-04-30\",\"value\":\"-0.17981312\"},\"389\":{\"date\":\"2057-05-31\",\"value\":\"-0.17461218\"},\"390\":{\"date\":\"2057-06-30\",\"value\":\"-0.16956173\"},\"391\":{\"date\":\"2057-07-31\",\"value\":\"-0.16465742\"},\"392\":{\"date\":\"2057-08-31\",\"value\":\"-0.15989501\"},\"393\":{\"date\":\"2057-09-30\",\"value\":\"-0.1569741\"},\"394\":{\"date\":\"2057-10-31\",\"value\":\"-0.15237941\"},\"395\":{\"date\":\"2057-11-30\",\"value\":\"-0.14791928\"},\"396\":{\"date\":\"2057-12-31\",\"value\":\"-0.14358976\"},\"397\":{\"date\":\"2058-01-31\",\"value\":\"-0.13938704\"},\"398\":{\"date\":\"2058-02-28\",\"value\":\"-0.13530739\"},\"399\":{\"date\":\"2058-03-31\",\"value\":\"-0.1313472\"},\"400\":{\"date\":\"2058-04-30\",\"value\":\"-0.12750299\"},\"401\":{\"date\":\"2058-05-31\",\"value\":\"-0.12377134\"},\"402\":{\"date\":\"2058-06-30\",\"value\":\"-0.12014897\"},\"403\":{\"date\":\"2058-07-31\",\"value\":\"-0.11663267\"},\"404\":{\"date\":\"2058-08-31\",\"value\":\"-0.11321933\"},\"405\":{\"date\":\"2058-09-30\",\"value\":\"-0.11125019\"},\"406\":{\"date\":\"2058-10-31\",\"value\":\"-0.10795081\"},\"407\":{\"date\":\"2058-11-30\",\"value\":\"-0.10474935\"},\"408\":{\"date\":\"2058-12-31\",\"value\":\"-0.10164289\"},\"409\":{\"date\":\"2059-01-31\",\"value\":\"-0.09862863\"},\"410\":{\"date\":\"2059-02-28\",\"value\":\"-0.09570382\"},\"411\":{\"date\":\"2059-03-31\",\"value\":\"-0.0928658\"},\"412\":{\"date\":\"2059-04-30\",\"value\":\"-0.09011199\"},\"413\":{\"date\":\"2059-05-31\",\"value\":\"-0.0874399\"},\"414\":{\"date\":\"2059-06-30\",\"value\":\"-0.0848471\"},\"415\":{\"date\":\"2059-07-31\",\"value\":\"-0.08233124\"},\"416\":{\"date\":\"2059-08-31\",\"value\":\"-0.07989003\"},\"417\":{\"date\":\"2059-09-30\",\"value\":\"-0.07855691\"},\"418\":{\"date\":\"2059-10-31\",\"value\":\"-0.07619358\"},\"419\":{\"date\":\"2059-11-30\",\"value\":\"-0.07390141\"},\"420\":{\"date\":\"2059-12-31\",\"value\":\"-0.07167826\"},\"421\":{\"date\":\"2060-01-31\",\"value\":\"-0.06952205\"},\"422\":{\"date\":\"2060-02-29\",\"value\":\"-0.06743075\"},\"423\":{\"date\":\"2060-03-31\",\"value\":\"-0.06540242\"},\"424\":{\"date\":\"2060-04-30\",\"value\":\"-0.06343515\"},\"425\":{\"date\":\"2060-05-31\",\"value\":\"-0.0615271\"},\"426\":{\"date\":\"2060-06-30\",\"value\":\"-0.0596765\"},\"427\":{\"date\":\"2060-07-31\",\"value\":\"-0.05788161\"},\"428\":{\"date\":\"2060-08-31\",\"value\":\"-0.05614075\"},\"429\":{\"date\":\"2060-09-30\",\"value\":\"-0.05523335\"},\"430\":{\"date\":\"2060-10-31\",\"value\":\"-0.0535461\"},\"431\":{\"date\":\"2060-11-30\",\"value\":\"-0.05191045\"},\"432\":{\"date\":\"2060-12-31\",\"value\":\"-0.05032482\"},\"433\":{\"date\":\"2061-01-31\",\"value\":\"-0.04878767\"},\"434\":{\"date\":\"2061-02-28\",\"value\":\"-0.04729752\"},\"435\":{\"date\":\"2061-03-31\",\"value\":\"-0.04585294\"},\"436\":{\"date\":\"2061-04-30\",\"value\":\"-0.04445253\"},\"437\":{\"date\":\"2061-05-31\",\"value\":\"-0.04309493\"},\"438\":{\"date\":\"2061-06-30\",\"value\":\"-0.04177884\"},\"439\":{\"date\":\"2061-07-31\",\"value\":\"-0.04050298\"},\"440\":{\"date\":\"2061-08-31\",\"value\":\"-0.03926613\"},\"441\":{\"date\":\"2061-09-30\",\"value\":\"-0.03864528\"},\"442\":{\"date\":\"2061-10-31\",\"value\":\"-0.03744559\"},\"443\":{\"date\":\"2061-11-30\",\"value\":\"-0.03628319\"},\"444\":{\"date\":\"2061-12-31\",\"value\":\"-0.03515692\"},\"445\":{\"date\":\"2062-01-31\",\"value\":\"-0.03406566\"},\"446\":{\"date\":\"2062-02-28\",\"value\":\"-0.03300832\"},\"447\":{\"date\":\"2062-03-31\",\"value\":\"-0.03198384\"},\"448\":{\"date\":\"2062-04-30\",\"value\":\"-0.03099119\"},\"449\":{\"date\":\"2062-05-31\",\"value\":\"-0.0300294\"},\"450\":{\"date\":\"2062-06-30\",\"value\":\"-0.02909749\"},\"451\":{\"date\":\"2062-07-31\",\"value\":\"-0.02819454\"},\"452\":{\"date\":\"2062-08-31\",\"value\":\"-0.02731964\"},\"453\":{\"date\":\"2062-09-30\",\"value\":\"-0.02689337\"},\"454\":{\"date\":\"2062-10-31\",\"value\":\"-0.02604437\"},\"455\":{\"date\":\"2062-11-30\",\"value\":\"-0.02522221\"},\"456\":{\"date\":\"2062-12-31\",\"value\":\"-0.02442605\"},\"457\":{\"date\":\"2063-01-31\",\"value\":\"-0.02365506\"},\"458\":{\"date\":\"2063-02-28\",\"value\":\"-0.02290844\"},\"459\":{\"date\":\"2063-03-31\",\"value\":\"-0.02218543\"},\"460\":{\"date\":\"2063-04-30\",\"value\":\"-0.02148526\"},\"461\":{\"date\":\"2063-05-31\",\"value\":\"-0.02080724\"},\"462\":{\"date\":\"2063-06-30\",\"value\":\"-0.02015064\"},\"463\":{\"date\":\"2063-07-31\",\"value\":\"-0.01951479\"},\"464\":{\"date\":\"2063-08-31\",\"value\":\"-0.01889904\"},\"465\":{\"date\":\"2063-09-30\",\"value\":\"-0.01860618\"},\"466\":{\"date\":\"2063-10-31\",\"value\":\"-0.01800851\"},\"467\":{\"date\":\"2063-11-30\",\"value\":\"-0.01743006\"},\"468\":{\"date\":\"2063-12-31\",\"value\":\"-0.01687024\"},\"469\":{\"date\":\"2064-01-31\",\"value\":\"-0.01632843\"},\"470\":{\"date\":\"2064-02-29\",\"value\":\"-0.01580405\"},\"471\":{\"date\":\"2064-03-31\",\"value\":\"-0.01529654\"},\"472\":{\"date\":\"2064-04-30\",\"value\":\"-0.01480536\"},\"473\":{\"date\":\"2064-05-31\",\"value\":\"-0.01432998\"},\"474\":{\"date\":\"2064-06-30\",\"value\":\"-0.0138699\"},\"475\":{\"date\":\"2064-07-31\",\"value\":\"-0.01342461\"},\"476\":{\"date\":\"2064-08-31\",\"value\":\"-0.01299364\"},\"477\":{\"date\":\"2064-09-30\",\"value\":\"-0.01279286\"},\"478\":{\"date\":\"2064-10-31\",\"value\":\"-0.0123745\"},\"479\":{\"date\":\"2064-11-30\",\"value\":\"-0.01196985\"},\"480\":{\"date\":\"2064-12-31\",\"value\":\"-0.01157846\"},\"481\":{\"date\":\"2065-01-31\",\"value\":\"-0.0111999\"},\"482\":{\"date\":\"2065-02-28\",\"value\":\"-0.01083374\"},\"483\":{\"date\":\"2065-03-31\",\"value\":\"-0.01047958\"},\"484\":{\"date\":\"2065-04-30\",\"value\":\"-0.01013702\"},\"485\":{\"date\":\"2065-05-31\",\"value\":\"-0.00980569\"},\"486\":{\"date\":\"2065-06-30\",\"value\":\"-0.0094852\"},\"487\":{\"date\":\"2065-07-31\",\"value\":\"-0.00917522\"},\"488\":{\"date\":\"2065-08-31\",\"value\":\"-0.00887538\"},\"489\":{\"date\":\"2065-09-30\",\"value\":\"-0.00873845\"},\"490\":{\"date\":\"2065-10-31\",\"value\":\"-0.00844737\"},\"491\":{\"date\":\"2065-11-30\",\"value\":\"-0.00816601\"},\"492\":{\"date\":\"2065-12-31\",\"value\":\"-0.00789404\"},\"493\":{\"date\":\"2066-01-31\",\"value\":\"-0.00763115\"},\"494\":{\"date\":\"2066-02-28\",\"value\":\"-0.00737704\"},\"495\":{\"date\":\"2066-03-31\",\"value\":\"-0.00713141\"},\"496\":{\"date\":\"2066-04-30\",\"value\":\"-0.00689398\"},\"497\":{\"date\":\"2066-05-31\",\"value\":\"-0.00666448\"},\"498\":{\"date\":\"2066-06-30\",\"value\":\"-0.00644263\"},\"499\":{\"date\":\"2066-07-31\",\"value\":\"-0.00622819\"},\"500\":{\"date\":\"2066-08-31\",\"value\":\"-0.0060209\"},\"501\":{\"date\":\"2066-09-30\",\"value\":\"-0.0059282\"},\"502\":{\"date\":\"2066-10-31\",\"value\":\"-0.00572694\"},\"503\":{\"date\":\"2066-11-30\",\"value\":\"-0.00553254\"},\"504\":{\"date\":\"2066-12-31\",\"value\":\"-0.00534475\"},\"505\":{\"date\":\"2067-01-31\",\"value\":\"-0.00516336\"},\"506\":{\"date\":\"2067-02-28\",\"value\":\"-0.00498814\"},\"507\":{\"date\":\"2067-03-31\",\"value\":\"-0.00481888\"},\"508\":{\"date\":\"2067-04-30\",\"value\":\"-0.00465539\"},\"509\":{\"date\":\"2067-05-31\",\"value\":\"-0.00449745\"},\"510\":{\"date\":\"2067-06-30\",\"value\":\"-0.00434489\"},\"511\":{\"date\":\"2067-07-31\",\"value\":\"-0.00419752\"},\"512\":{\"date\":\"2067-08-31\",\"value\":\"-0.00405516\"},\"513\":{\"date\":\"2067-09-30\",\"value\":\"-0.00399298\"},\"514\":{\"date\":\"2067-10-31\",\"value\":\"-0.00385475\"},\"515\":{\"date\":\"2067-11-30\",\"value\":\"-0.00372131\"},\"516\":{\"date\":\"2067-12-31\",\"value\":\"-0.00359251\"},\"517\":{\"date\":\"2068-01-31\",\"value\":\"-0.00346818\"},\"518\":{\"date\":\"2068-02-29\",\"value\":\"-0.00334816\"},\"519\":{\"date\":\"2068-03-31\",\"value\":\"-0.00323232\"},\"520\":{\"date\":\"2068-04-30\",\"value\":\"-0.00312049\"},\"521\":{\"date\":\"2068-05-31\",\"value\":\"-0.00301255\"},\"522\":{\"date\":\"2068-06-30\",\"value\":\"-0.00290835\"},\"523\":{\"date\":\"2068-07-31\",\"value\":\"-0.00280776\"},\"524\":{\"date\":\"2068-08-31\",\"value\":\"-0.00271067\"},\"525\":{\"date\":\"2068-09-30\",\"value\":\"-0.00266936\"},\"526\":{\"date\":\"2068-10-31\",\"value\":\"-0.00257506\"},\"527\":{\"date\":\"2068-11-30\",\"value\":\"-0.0024841\"},\"528\":{\"date\":\"2068-12-31\",\"value\":\"-0.00239636\"},\"529\":{\"date\":\"2069-01-31\",\"value\":\"-0.00231174\"},\"530\":{\"date\":\"2069-02-28\",\"value\":\"-0.00223011\"},\"531\":{\"date\":\"2069-03-31\",\"value\":\"-0.00215137\"},\"532\":{\"date\":\"2069-04-30\",\"value\":\"-0.00207543\"},\"533\":{\"date\":\"2069-05-31\",\"value\":\"-0.00200218\"},\"534\":{\"date\":\"2069-06-30\",\"value\":\"-0.00193152\"},\"535\":{\"date\":\"2069-07-31\",\"value\":\"-0.00186336\"},\"536\":{\"date\":\"2069-08-31\",\"value\":\"-0.00179762\"},\"537\":{\"date\":\"2069-09-30\",\"value\":\"-0.00177045\"},\"538\":{\"date\":\"2069-10-31\",\"value\":\"-0.00170658\"},\"539\":{\"date\":\"2069-11-30\",\"value\":\"-0.00164501\"},\"540\":{\"date\":\"2069-12-31\",\"value\":\"-0.00158568\"},\"541\":{\"date\":\"2070-01-31\",\"value\":\"-0.0015285\"},\"542\":{\"date\":\"2070-02-28\",\"value\":\"-0.00147339\"},\"543\":{\"date\":\"2070-03-31\",\"value\":\"-0.00142028\"},\"544\":{\"date\":\"2070-04-30\",\"value\":\"-0.00136908\"},\"545\":{\"date\":\"2070-05-31\",\"value\":\"-0.00131974\"},\"546\":{\"date\":\"2070-06-30\",\"value\":\"-0.00127219\"},\"547\":{\"date\":\"2070-07-31\",\"value\":\"-0.00122635\"},\"548\":{\"date\":\"2070-08-31\",\"value\":\"-0.00118218\"},\"549\":{\"date\":\"2070-09-30\",\"value\":\"-0.00116447\"},\"550\":{\"date\":\"2070-10-31\",\"value\":\"-0.00112153\"},\"551\":{\"date\":\"2070-11-30\",\"value\":\"-0.00108019\"},\"552\":{\"date\":\"2070-12-31\",\"value\":\"-0.00104037\"},\"553\":{\"date\":\"2071-01-31\",\"value\":\"-0.00100203\"},\"554\":{\"date\":\"2071-02-28\",\"value\":\"-0.00096511\"},\"555\":{\"date\":\"2071-03-31\",\"value\":\"-0.00092956\"},\"556\":{\"date\":\"2071-04-30\",\"value\":\"-0.00089533\"},\"557\":{\"date\":\"2071-05-31\",\"value\":\"-0.00086236\"},\"558\":{\"date\":\"2071-06-30\",\"value\":\"-0.00083061\"},\"559\":{\"date\":\"2071-07-31\",\"value\":\"-0.00080003\"},\"560\":{\"date\":\"2071-08-31\",\"value\":\"-0.00077058\"},\"561\":{\"date\":\"2071-09-30\",\"value\":\"-0.00075914\"},\"562\":{\"date\":\"2071-10-31\",\"value\":\"-0.00073051\"},\"563\":{\"date\":\"2071-11-30\",\"value\":\"-0.00070297\"},\"564\":{\"date\":\"2071-12-31\",\"value\":\"-0.00067647\"},\"565\":{\"date\":\"2072-01-31\",\"value\":\"-0.00065098\"},\"566\":{\"date\":\"2072-02-29\",\"value\":\"-0.00062645\"},\"567\":{\"date\":\"2072-03-31\",\"value\":\"-0.00060285\"},\"568\":{\"date\":\"2072-04-30\",\"value\":\"-0.00058014\"},\"569\":{\"date\":\"2072-05-31\",\"value\":\"-0.0005583\"},\"570\":{\"date\":\"2072-06-30\",\"value\":\"-0.00053728\"},\"571\":{\"date\":\"2072-07-31\",\"value\":\"-0.00051706\"},\"572\":{\"date\":\"2072-08-31\",\"value\":\"-0.0004976\"},\"573\":{\"date\":\"2072-09-30\",\"value\":\"-0.00049026\"},\"574\":{\"date\":\"2072-10-31\",\"value\":\"-0.00047134\"},\"575\":{\"date\":\"2072-11-30\",\"value\":\"-0.00045315\"},\"576\":{\"date\":\"2072-12-31\",\"value\":\"-0.00043567\"},\"577\":{\"date\":\"2073-01-31\",\"value\":\"-0.00041887\"},\"578\":{\"date\":\"2073-02-28\",\"value\":\"-0.00040271\"},\"579\":{\"date\":\"2073-03-31\",\"value\":\"-0.00038719\"},\"580\":{\"date\":\"2073-04-30\",\"value\":\"-0.00037227\"},\"581\":{\"date\":\"2073-05-31\",\"value\":\"-0.00035792\"},\"582\":{\"date\":\"2073-06-30\",\"value\":\"-0.00034414\"},\"583\":{\"date\":\"2073-07-31\",\"value\":\"-0.00033088\"},\"584\":{\"date\":\"2073-08-31\",\"value\":\"-0.00031814\"},\"585\":{\"date\":\"2073-09-30\",\"value\":\"-0.00031348\"},\"586\":{\"date\":\"2073-10-31\",\"value\":\"-0.00030109\"},\"587\":{\"date\":\"2073-11-30\",\"value\":\"-0.00028919\"},\"588\":{\"date\":\"2073-12-31\",\"value\":\"-0.00027776\"},\"589\":{\"date\":\"2074-01-31\",\"value\":\"-0.00026679\"},\"590\":{\"date\":\"2074-02-28\",\"value\":\"-0.00025625\"},\"591\":{\"date\":\"2074-03-31\",\"value\":\"-0.00024614\"},\"592\":{\"date\":\"2074-04-30\",\"value\":\"-0.00023643\"},\"593\":{\"date\":\"2074-05-31\",\"value\":\"-0.0002271\"},\"594\":{\"date\":\"2074-06-30\",\"value\":\"-0.00021814\"},\"595\":{\"date\":\"2074-07-31\",\"value\":\"-0.00020954\"},\"596\":{\"date\":\"2074-08-31\",\"value\":\"-0.00020128\"},\"597\":{\"date\":\"2074-09-30\",\"value\":\"-0.00019835\"},\"598\":{\"date\":\"2074-10-31\",\"value\":\"-0.00019031\"},\"599\":{\"date\":\"2074-11-30\",\"value\":\"-0.0001826\"},\"600\":{\"date\":\"2074-12-31\",\"value\":\"-0.00017521\"},\"601\":{\"date\":\"2075-01-31\",\"value\":\"-0.00016812\"},\"602\":{\"date\":\"2075-02-28\",\"value\":\"-0.00016132\"},\"603\":{\"date\":\"2075-03-31\",\"value\":\"-0.00015479\"},\"604\":{\"date\":\"2075-04-30\",\"value\":\"-0.00014853\"},\"605\":{\"date\":\"2075-05-31\",\"value\":\"-0.00014253\"},\"606\":{\"date\":\"2075-06-30\",\"value\":\"-0.00013677\"},\"607\":{\"date\":\"2075-07-31\",\"value\":\"-0.00013125\"},\"608\":{\"date\":\"2075-08-31\",\"value\":\"-0.00012595\"},\"609\":{\"date\":\"2075-09-30\",\"value\":\"-0.00012413\"},\"610\":{\"date\":\"2075-10-31\",\"value\":\"-0.00011897\"},\"611\":{\"date\":\"2075-11-30\",\"value\":\"-0.00011403\"},\"612\":{\"date\":\"2075-12-31\",\"value\":\"-0.00010929\"},\"613\":{\"date\":\"2076-01-31\",\"value\":\"-0.00010476\"},\"614\":{\"date\":\"2076-02-29\",\"value\":\"-0.00010041\"},\"615\":{\"date\":\"2076-03-31\",\"value\":\"-0.00009625\"},\"616\":{\"date\":\"2076-04-30\",\"value\":\"-0.00009226\"},\"617\":{\"date\":\"2076-05-31\",\"value\":\"-0.00008844\"},\"618\":{\"date\":\"2076-06-30\",\"value\":\"-0.00008478\"},\"619\":{\"date\":\"2076-07-31\",\"value\":\"-0.00008127\"},\"620\":{\"date\":\"2076-08-31\",\"value\":\"-0.00007791\"},\"621\":{\"date\":\"2076-09-30\",\"value\":\"-0.00007679\"},\"622\":{\"date\":\"2076-10-31\",\"value\":\"-0.00007352\"},\"623\":{\"date\":\"2076-11-30\",\"value\":\"-0.00007038\"},\"624\":{\"date\":\"2076-12-31\",\"value\":\"-0.00006739\"},\"625\":{\"date\":\"2077-01-31\",\"value\":\"-0.00006452\"},\"626\":{\"date\":\"2077-02-28\",\"value\":\"-0.00006177\"},\"627\":{\"date\":\"2077-03-31\",\"value\":\"-0.00005915\"},\"628\":{\"date\":\"2077-04-30\",\"value\":\"-0.00005663\"},\"629\":{\"date\":\"2077-05-31\",\"value\":\"-0.00005423\"},\"630\":{\"date\":\"2077-06-30\",\"value\":\"-0.00005192\"},\"631\":{\"date\":\"2077-07-31\",\"value\":\"-0.00004972\"},\"632\":{\"date\":\"2077-08-31\",\"value\":\"-0.00004761\"},\"633\":{\"date\":\"2077-09-30\",\"value\":\"-0.00004695\"},\"634\":{\"date\":\"2077-10-31\",\"value\":\"-0.00004489\"},\"635\":{\"date\":\"2077-11-30\",\"value\":\"-0.00004293\"},\"636\":{\"date\":\"2077-12-31\",\"value\":\"-0.00004105\"},\"637\":{\"date\":\"2078-01-31\",\"value\":\"-0.00003926\"},\"638\":{\"date\":\"2078-02-28\",\"value\":\"-0.00003754\"},\"639\":{\"date\":\"2078-03-31\",\"value\":\"-0.00003591\"},\"640\":{\"date\":\"2078-04-30\",\"value\":\"-0.00003434\"},\"641\":{\"date\":\"2078-05-31\",\"value\":\"-0.00003284\"},\"642\":{\"date\":\"2078-06-30\",\"value\":\"-0.00003141\"},\"643\":{\"date\":\"2078-07-31\",\"value\":\"-0.00003005\"},\"644\":{\"date\":\"2078-08-31\",\"value\":\"-0.00002874\"},\"645\":{\"date\":\"2078-09-30\",\"value\":\"-0.00002837\"},\"646\":{\"date\":\"2078-10-31\",\"value\":\"-0.00002707\"},\"647\":{\"date\":\"2078-11-30\",\"value\":\"-0.00002586\"},\"648\":{\"date\":\"2078-12-31\",\"value\":\"-0.0000247\"},\"649\":{\"date\":\"2079-01-31\",\"value\":\"-0.00002359\"},\"650\":{\"date\":\"2079-02-28\",\"value\":\"-0.00002253\"},\"651\":{\"date\":\"2079-03-31\",\"value\":\"-0.00002152\"},\"652\":{\"date\":\"2079-04-30\",\"value\":\"-0.00002056\"},\"653\":{\"date\":\"2079-05-31\",\"value\":\"-0.00001964\"},\"654\":{\"date\":\"2079-06-30\",\"value\":\"-0.00001876\"},\"655\":{\"date\":\"2079-07-31\",\"value\":\"-0.00001792\"},\"656\":{\"date\":\"2079-08-31\",\"value\":\"-0.00001712\"},\"657\":{\"date\":\"2079-09-30\",\"value\":\"-0.0000169\"},\"658\":{\"date\":\"2079-10-31\",\"value\":\"-0.00001612\"},\"659\":{\"date\":\"2079-11-30\",\"value\":\"-0.00001538\"},\"660\":{\"date\":\"2079-12-31\",\"value\":\"-0.00001467\"},\"661\":{\"date\":\"2080-01-31\",\"value\":\"-0.00001399\"},\"662\":{\"date\":\"2080-02-29\",\"value\":\"-0.00001335\"},\"663\":{\"date\":\"2080-03-31\",\"value\":\"-0.00001273\"},\"664\":{\"date\":\"2080-04-30\",\"value\":\"-0.00001215\"},\"665\":{\"date\":\"2080-05-31\",\"value\":\"-0.00001159\"},\"666\":{\"date\":\"2080-06-30\",\"value\":\"-0.00001106\"},\"667\":{\"date\":\"2080-07-31\",\"value\":\"-0.00001055\"},\"668\":{\"date\":\"2080-08-31\",\"value\":\"-0.00001007\"},\"669\":{\"date\":\"2080-09-30\",\"value\":\"-0.00000995\"},\"670\":{\"date\":\"2080-10-31\",\"value\":\"-0.00000947\"},\"671\":{\"date\":\"2080-11-30\",\"value\":\"-0.00000902\"},\"672\":{\"date\":\"2080-12-31\",\"value\":\"-0.0000086\"},\"673\":{\"date\":\"2081-01-31\",\"value\":\"-0.00000819\"},\"674\":{\"date\":\"2081-02-28\",\"value\":\"-0.0000078\"},\"675\":{\"date\":\"2081-03-31\",\"value\":\"-0.00000743\"},\"676\":{\"date\":\"2081-04-30\",\"value\":\"-0.00000708\"},\"677\":{\"date\":\"2081-05-31\",\"value\":\"-0.00000675\"},\"678\":{\"date\":\"2081-06-30\",\"value\":\"-0.00000643\"},\"679\":{\"date\":\"2081-07-31\",\"value\":\"-0.00000612\"},\"680\":{\"date\":\"2081-08-31\",\"value\":\"-0.00000584\"},\"681\":{\"date\":\"2081-09-30\",\"value\":\"-0.00000577\"},\"682\":{\"date\":\"2081-10-31\",\"value\":\"-0.00000549\"},\"683\":{\"date\":\"2081-11-30\",\"value\":\"-0.00000522\"},\"684\":{\"date\":\"2081-12-31\",\"value\":\"-0.00000497\"},\"685\":{\"date\":\"2082-01-31\",\"value\":\"-0.00000472\"},\"686\":{\"date\":\"2082-02-28\",\"value\":\"-0.00000449\"},\"687\":{\"date\":\"2082-03-31\",\"value\":\"-0.00000427\"},\"688\":{\"date\":\"2082-04-30\",\"value\":\"-0.00000407\"},\"689\":{\"date\":\"2082-05-31\",\"value\":\"-0.00000387\"},\"690\":{\"date\":\"2082-06-30\",\"value\":\"-0.00000368\"},\"691\":{\"date\":\"2082-07-31\",\"value\":\"-0.0000035\"},\"692\":{\"date\":\"2082-08-31\",\"value\":\"-0.00000333\"},\"693\":{\"date\":\"2082-09-30\",\"value\":\"-0.0000033\"},\"694\":{\"date\":\"2082-10-31\",\"value\":\"-0.00000313\"},\"695\":{\"date\":\"2082-11-30\",\"value\":\"-0.00000298\"},\"696\":{\"date\":\"2082-12-31\",\"value\":\"-0.00000283\"},\"697\":{\"date\":\"2083-01-31\",\"value\":\"-0.00000268\"},\"698\":{\"date\":\"2083-02-28\",\"value\":\"-0.00000255\"},\"699\":{\"date\":\"2083-03-31\",\"value\":\"-0.00000242\"},\"700\":{\"date\":\"2083-04-30\",\"value\":\"-0.0000023\"},\"701\":{\"date\":\"2083-05-31\",\"value\":\"-0.00000218\"},\"702\":{\"date\":\"2083-06-30\",\"value\":\"-0.00000207\"},\"703\":{\"date\":\"2083-07-31\",\"value\":\"-0.00000197\"},\"704\":{\"date\":\"2083-08-31\",\"value\":\"-0.00000187\"},\"705\":{\"date\":\"2083-09-30\",\"value\":\"-0.00000186\"},\"706\":{\"date\":\"2083-10-31\",\"value\":\"-0.00000176\"},\"707\":{\"date\":\"2083-11-30\",\"value\":\"-0.00000167\"},\"708\":{\"date\":\"2083-12-31\",\"value\":\"-0.00000158\"},\"709\":{\"date\":\"2084-01-31\",\"value\":\"-0.0000015\"},\"710\":{\"date\":\"2084-02-29\",\"value\":\"-0.00000142\"},\"711\":{\"date\":\"2084-03-31\",\"value\":\"-0.00000135\"},\"712\":{\"date\":\"2084-04-30\",\"value\":\"-0.00000128\"},\"713\":{\"date\":\"2084-05-31\",\"value\":\"-0.00000121\"},\"714\":{\"date\":\"2084-06-30\",\"value\":\"-0.00000115\"},\"715\":{\"date\":\"2084-07-31\",\"value\":\"-0.00000109\"},\"716\":{\"date\":\"2084-08-31\",\"value\":\"-0.00000103\"},\"717\":{\"date\":\"2084-09-30\",\"value\":\"-0.00000103\"},\"718\":{\"date\":\"2084-10-31\",\"value\":\"-0.00000097\"},\"719\":{\"date\":\"2084-11-30\",\"value\":\"-0.00000092\"},\"720\":{\"date\":\"2084-12-31\",\"value\":\"-0.00000087\"},\"721\":{\"date\":\"2085-01-31\",\"value\":\"-0.00000082\"},\"722\":{\"date\":\"2085-02-28\",\"value\":\"-0.00000078\"},\"723\":{\"date\":\"2085-03-31\",\"value\":\"-0.00000074\"},\"724\":{\"date\":\"2085-04-30\",\"value\":\"-0.00000070\"},\"725\":{\"date\":\"2085-05-31\",\"value\":\"-0.00000066\"},\"726\":{\"date\":\"2085-06-30\",\"value\":\"-0.00000062\"},\"727\":{\"date\":\"2085-07-31\",\"value\":\"-0.00000059\"},\"728\":{\"date\":\"2085-08-31\",\"value\":\"-0.00000056\"},\"729\":{\"date\":\"2085-09-30\",\"value\":\"-0.00000727\"},\"730\":{\"date\":\"2085-10-31\",\"value\":\"-0.00000011\"},\"731\":{\"date\":\"2085-11-30\",\"value\":\"-0.00000010\"},\"732\":{\"date\":\"2085-12-31\",\"value\":\"-0.00000010\"},\"733\":{\"date\":\"2086-01-31\",\"value\":\"-0.000000090\"},\"734\":{\"date\":\"2086-02-28\",\"value\":\"-0.000000090\"},\"735\":{\"date\":\"2086-03-31\",\"value\":\"-0.000000090\"},\"736\":{\"date\":\"2086-04-30\",\"value\":\"-0.000000080\"},\"737\":{\"date\":\"2086-05-31\",\"value\":\"-0.000000080\"},\"738\":{\"date\":\"2086-06-30\",\"value\":\"-0.000000080\"},\"739\":{\"date\":\"2086-07-31\",\"value\":\"-0.000000070\"},\"740\":{\"date\":\"2086-08-31\",\"value\":\"-0.000000070\"},\"741\":{\"date\":\"2086-09-30\",\"value\":\"-0.000000070\"},\"742\":{\"date\":\"2086-10-31\",\"value\":\"-0.000000070\"},\"743\":{\"date\":\"2086-11-30\",\"value\":\"-0.000000060\"},\"744\":{\"date\":\"2086-12-31\",\"value\":\"-0.000000060\"},\"745\":{\"date\":\"2087-01-31\",\"value\":\"-0.000000060\"},\"746\":{\"date\":\"2087-02-28\",\"value\":\"-0.000000060\"},\"747\":{\"date\":\"2087-03-31\",\"value\":\"-0.000000050\"},\"748\":{\"date\":\"2087-04-30\",\"value\":\"-0.000000050\"},\"749\":{\"date\":\"2087-05-31\",\"value\":\"-0.000000050\"},\"750\":{\"date\":\"2087-06-30\",\"value\":\"-0.000000050\"},\"751\":{\"date\":\"2087-07-31\",\"value\":\"-0.000000040\"},\"752\":{\"date\":\"2087-08-31\",\"value\":\"-0.000000040\"},\"753\":{\"date\":\"2087-09-30\",\"value\":\"-0.000000040\"},\"754\":{\"date\":\"2087-10-31\",\"value\":\"-0.000000040\"},\"755\":{\"date\":\"2087-11-30\",\"value\":\"-0.000000040\"},\"756\":{\"date\":\"2087-12-31\",\"value\":\"-0.000000040\"},\"757\":{\"date\":\"2088-01-31\",\"value\":\"-0.000000040\"},\"758\":{\"date\":\"2088-02-29\",\"value\":\"-0.000000030\"},\"759\":{\"date\":\"2088-03-31\",\"value\":\"-0.000000030\"},\"760\":{\"date\":\"2088-04-30\",\"value\":\"-0.000000030\"},\"761\":{\"date\":\"2088-05-31\",\"value\":\"-0.000000030\"},\"762\":{\"date\":\"2088-06-30\",\"value\":\"-0.000000030\"},\"763\":{\"date\":\"2088-07-31\",\"value\":\"-0.000000030\"},\"764\":{\"date\":\"2088-08-31\",\"value\":\"-0.000000030\"},\"765\":{\"date\":\"2088-09-30\",\"value\":\"-0.000000030\"},\"766\":{\"date\":\"2088-10-31\",\"value\":\"-0.000000020\"},\"767\":{\"date\":\"2088-11-30\",\"value\":\"-0.000000020\"},\"768\":{\"date\":\"2088-12-31\",\"value\":\"-0.000000020\"},\"769\":{\"date\":\"2089-01-31\",\"value\":\"-0.000000020\"},\"770\":{\"date\":\"2089-02-28\",\"value\":\"-0.000000020\"},\"771\":{\"date\":\"2089-03-31\",\"value\":\"-0.000000020\"},\"772\":{\"date\":\"2089-04-30\",\"value\":\"-0.000000020\"},\"773\":{\"date\":\"2089-05-31\",\"value\":\"-0.000000020\"},\"774\":{\"date\":\"2089-06-30\",\"value\":\"-0.000000020\"},\"775\":{\"date\":\"2089-07-31\",\"value\":\"-0.000000020\"},\"776\":{\"date\":\"2089-08-31\",\"value\":\"-0.000000020\"},\"777\":{\"date\":\"2089-09-30\",\"value\":\"-0.000000020\"},\"778\":{\"date\":\"2089-10-31\",\"value\":\"-0.000000010\"},\"779\":{\"date\":\"2089-11-30\",\"value\":\"-0.000000010\"},\"780\":{\"date\":\"2089-12-31\",\"value\":\"-0.000000010\"},\"781\":{\"date\":\"2090-01-31\",\"value\":\"-0.000000010\"},\"782\":{\"date\":\"2090-02-28\",\"value\":\"-0.000000010\"},\"783\":{\"date\":\"2090-03-31\",\"value\":\"-0.000000010\"},\"784\":{\"date\":\"2090-04-30\",\"value\":\"-0.000000010\"},\"785\":{\"date\":\"2090-05-31\",\"value\":\"-0.000000010\"},\"786\":{\"date\":\"2090-06-30\",\"value\":\"-0.000000010\"},\"787\":{\"date\":\"2090-07-31\",\"value\":\"-0.000000010\"},\"788\":{\"date\":\"2090-08-31\",\"value\":\"-0.000000010\"},\"789\":{\"date\":\"2090-09-30\",\"value\":\"-0.000000010\"},\"790\":{\"date\":\"2090-10-31\",\"value\":\"-0.000000010\"},\"791\":{\"date\":\"2090-11-30\",\"value\":\"-0.000000010\"},\"792\":{\"date\":\"2090-12-31\",\"value\":\"-0.000000010\"},\"793\":{\"date\":\"2091-01-31\",\"value\":\"-0.000000010\"},\"794\":{\"date\":\"2091-02-28\",\"value\":\"-0.000000010\"},\"795\":{\"date\":\"2091-03-31\",\"value\":\"-0.000000010\"},\"796\":{\"date\":\"2091-04-30\",\"value\":\"-0.000000010\"},\"797\":{\"date\":\"2091-05-31\",\"value\":\"-0.000000010\"},\"798\":{\"date\":\"2091-06-30\",\"value\":\"-0.000000010\"},\"799\":{\"date\":\"2091-07-31\",\"value\":\"-0.000000010\"},\"800\":{\"date\":\"2091-08-31\",\"value\":\"-0.000000010\"},\"801\":{\"date\":\"2091-09-30\",\"value\":\"-0.000000010\"},\"802\":{\"date\":\"2091-10-31\",\"value\":\"0\"},\"803\":{\"date\":\"2091-11-30\",\"value\":\"0\"},\"804\":{\"date\":\"2091-12-31\",\"value\":\"0\"},\"805\":{\"date\":\"2092-01-31\",\"value\":\"0\"},\"806\":{\"date\":\"2092-02-29\",\"value\":\"0\"},\"807\":{\"date\":\"2092-03-31\",\"value\":\"0\"},\"808\":{\"date\":\"2092-04-30\",\"value\":\"0\"},\"809\":{\"date\":\"2092-05-31\",\"value\":\"0\"},\"810\":{\"date\":\"2092-06-30\",\"value\":\"0\"},\"811\":{\"date\":\"2092-07-31\",\"value\":\"0\"},\"812\":{\"date\":\"2092-08-31\",\"value\":\"0\"},\"813\":{\"date\":\"2092-09-30\",\"value\":\"0\"},\"814\":{\"date\":\"2092-10-31\",\"value\":\"0\"},\"815\":{\"date\":\"2092-11-30\",\"value\":\"0\"},\"816\":{\"date\":\"2092-12-31\",\"value\":\"0\"},\"817\":{\"date\":\"2093-01-31\",\"value\":\"0\"},\"818\":{\"date\":\"2093-02-28\",\"value\":\"0\"},\"819\":{\"date\":\"2093-03-31\",\"value\":\"0\"},\"820\":{\"date\":\"2093-04-30\",\"value\":\"0\"},\"821\":{\"date\":\"2093-05-31\",\"value\":\"0\"},\"822\":{\"date\":\"2093-06-30\",\"value\":\"0\"},\"823\":{\"date\":\"2093-07-31\",\"value\":\"0\"},\"824\":{\"date\":\"2093-08-31\",\"value\":\"0\"},\"825\":{\"date\":\"2093-09-30\",\"value\":\"-0.000000020\"},\"826\":{\"date\":\"2093-10-31\",\"value\":\"0\"},\"827\":{\"date\":\"2093-11-30\",\"value\":\"0\"},\"828\":{\"date\":\"2093-12-31\",\"value\":\"0\"},\"829\":{\"date\":\"2094-01-31\",\"value\":\"0\"},\"830\":{\"date\":\"2094-02-28\",\"value\":\"0\"},\"831\":{\"date\":\"2094-03-31\",\"value\":\"0\"},\"832\":{\"date\":\"2094-04-30\",\"value\":\"0\"},\"833\":{\"date\":\"2094-05-31\",\"value\":\"0\"},\"834\":{\"date\":\"2094-06-30\",\"value\":\"0\"},\"835\":{\"date\":\"2094-07-31\",\"value\":\"0\"},\"836\":{\"date\":\"2094-08-31\",\"value\":\"0\"},\"837\":{\"date\":\"2094-09-30\",\"value\":\"0\"},\"838\":{\"date\":\"2094-10-31\",\"value\":\"0\"},\"839\":{\"date\":\"2094-11-30\",\"value\":\"0\"},\"840\":{\"date\":\"2094-12-31\",\"value\":\"0\"},\"841\":{\"date\":\"2095-01-31\",\"value\":\"0\"},\"842\":{\"date\":\"2095-02-28\",\"value\":\"0\"},\"843\":{\"date\":\"2095-03-31\",\"value\":\"0\"},\"844\":{\"date\":\"2095-04-30\",\"value\":\"0\"},\"845\":{\"date\":\"2095-05-31\",\"value\":\"0\"},\"846\":{\"date\":\"2095-06-30\",\"value\":\"0\"},\"847\":{\"date\":\"2095-07-31\",\"value\":\"0\"},\"848\":{\"date\":\"2095-08-31\",\"value\":\"0\"},\"849\":{\"date\":\"2095-09-30\",\"value\":\"0\"},\"850\":{\"date\":\"2095-10-31\",\"value\":\"0\"},\"851\":{\"date\":\"2095-11-30\",\"value\":\"0\"},\"852\":{\"date\":\"2095-12-31\",\"value\":\"0\"},\"853\":{\"date\":\"2096-01-31\",\"value\":\"0\"},\"854\":{\"date\":\"2096-02-29\",\"value\":\"0\"},\"855\":{\"date\":\"2096-03-31\",\"value\":\"0\"},\"856\":{\"date\":\"2096-04-30\",\"value\":\"0\"},\"857\":{\"date\":\"2096-05-31\",\"value\":\"0\"},\"858\":{\"date\":\"2096-06-30\",\"value\":\"0\"},\"859\":{\"date\":\"2096-07-31\",\"value\":\"0\"},\"860\":{\"date\":\"2096-08-31\",\"value\":\"0\"},\"861\":{\"date\":\"2096-09-30\",\"value\":\"0\"},\"862\":{\"date\":\"2096-10-31\",\"value\":\"0\"},\"863\":{\"date\":\"2096-11-30\",\"value\":\"0\"},\"864\":{\"date\":\"2096-12-31\",\"value\":\"0\"},\"865\":{\"date\":\"2097-01-31\",\"value\":\"0\"},\"866\":{\"date\":\"2097-02-28\",\"value\":\"0\"},\"867\":{\"date\":\"2097-03-31\",\"value\":\"0\"},\"868\":{\"date\":\"2097-04-30\",\"value\":\"0\"},\"869\":{\"date\":\"2097-05-31\",\"value\":\"0\"},\"870\":{\"date\":\"2097-06-30\",\"value\":\"0\"},\"871\":{\"date\":\"2097-07-31\",\"value\":\"0\"},\"872\":{\"date\":\"2097-08-31\",\"value\":\"0\"},\"873\":{\"date\":\"2097-09-30\",\"value\":\"0\"},\"874\":{\"date\":\"2097-10-31\",\"value\":\"0\"},\"875\":{\"date\":\"2097-11-30\",\"value\":\"0\"},\"876\":{\"date\":\"2097-12-31\",\"value\":\"0\"},\"877\":{\"date\":\"2098-01-31\",\"value\":\"0\"},\"878\":{\"date\":\"2098-02-28\",\"value\":\"0\"},\"879\":{\"date\":\"2098-03-31\",\"value\":\"0\"},\"880\":{\"date\":\"2098-04-30\",\"value\":\"0\"},\"881\":{\"date\":\"2098-05-31\",\"value\":\"0\"},\"882\":{\"date\":\"2098-06-30\",\"value\":\"0\"},\"883\":{\"date\":\"2098-07-31\",\"value\":\"0\"},\"884\":{\"date\":\"2098-08-31\",\"value\":\"0\"},\"885\":{\"date\":\"2098-09-30\",\"value\":\"0\"},\"886\":{\"date\":\"2098-10-31\",\"value\":\"0\"},\"887\":{\"date\":\"2098-11-30\",\"value\":\"0\"},\"888\":{\"date\":\"2098-12-31\",\"value\":\"0\"},\"889\":{\"date\":\"2099-01-31\",\"value\":\"0\"},\"890\":{\"date\":\"2099-02-28\",\"value\":\"0\"},\"891\":{\"date\":\"2099-03-31\",\"value\":\"0\"},\"892\":{\"date\":\"2099-04-30\",\"value\":\"0\"},\"893\":{\"date\":\"2099-05-31\",\"value\":\"0\"},\"894\":{\"date\":\"2099-06-30\",\"value\":\"0\"},\"895\":{\"date\":\"2099-07-31\",\"value\":\"0\"},\"896\":{\"date\":\"2099-08-31\",\"value\":\"0\"},\"897\":{\"date\":\"2099-09-30\",\"value\":\"0\"},\"898\":{\"date\":\"2099-10-31\",\"value\":\"0\"},\"899\":{\"date\":\"2099-11-30\",\"value\":\"0\"},\"900\":{\"date\":\"2099-12-31\",\"value\":\"0\"},\"901\":{\"date\":\"2100-01-31\",\"value\":\"0\"},\"902\":{\"date\":\"2100-02-28\",\"value\":\"0\"},\"903\":{\"date\":\"2100-03-31\",\"value\":\"0\"},\"904\":{\"date\":\"2100-04-30\",\"value\":\"0\"},\"905\":{\"date\":\"2100-05-31\",\"value\":\"0\"},\"906\":{\"date\":\"2100-06-30\",\"value\":\"0\"},\"907\":{\"date\":\"2100-07-31\",\"value\":\"0\"},\"908\":{\"date\":\"2100-08-31\",\"value\":\"0\"},\"909\":{\"date\":\"2100-09-30\",\"value\":\"0\"},\"910\":{\"date\":\"2100-10-31\",\"value\":\"0\"},\"911\":{\"date\":\"2100-11-30\",\"value\":\"0\"},\"912\":{\"date\":\"2100-12-31\",\"value\":\"0\"},\"913\":{\"date\":\"2101-01-31\",\"value\":\"0\"},\"914\":{\"date\":\"2101-02-28\",\"value\":\"0\"},\"915\":{\"date\":\"2101-03-31\",\"value\":\"0\"},\"916\":{\"date\":\"2101-04-30\",\"value\":\"0\"},\"917\":{\"date\":\"2101-05-31\",\"value\":\"0\"},\"918\":{\"date\":\"2101-06-30\",\"value\":\"0\"},\"919\":{\"date\":\"2101-07-31\",\"value\":\"0\"},\"920\":{\"date\":\"2101-08-31\",\"value\":\"0\"},\"921\":{\"date\":\"2101-09-30\",\"value\":\"0\"},\"922\":{\"date\":\"2101-10-31\",\"value\":\"0\"},\"923\":{\"date\":\"2101-11-30\",\"value\":\"0\"},\"924\":{\"date\":\"2101-12-31\",\"value\":\"0\"},\"925\":{\"date\":\"2102-01-31\",\"value\":\"0\"},\"926\":{\"date\":\"2102-02-28\",\"value\":\"0\"},\"927\":{\"date\":\"2102-03-31\",\"value\":\"0\"},\"928\":{\"date\":\"2102-04-30\",\"value\":\"0\"},\"929\":{\"date\":\"2102-05-31\",\"value\":\"0\"},\"930\":{\"date\":\"2102-06-30\",\"value\":\"0\"},\"931\":{\"date\":\"2102-07-31\",\"value\":\"0\"},\"932\":{\"date\":\"2102-08-31\",\"value\":\"0\"},\"933\":{\"date\":\"2102-09-30\",\"value\":\"0\"},\"934\":{\"date\":\"2102-10-31\",\"value\":\"0\"},\"935\":{\"date\":\"2102-11-30\",\"value\":\"0\"},\"936\":{\"date\":\"2102-12-31\",\"value\":\"0\"},\"937\":{\"date\":\"2103-01-31\",\"value\":\"0\"},\"938\":{\"date\":\"2103-02-28\",\"value\":\"0\"},\"939\":{\"date\":\"2103-03-31\",\"value\":\"0\"},\"940\":{\"date\":\"2103-04-30\",\"value\":\"0\"},\"941\":{\"date\":\"2103-05-31\",\"value\":\"0\"},\"942\":{\"date\":\"2103-06-30\",\"value\":\"0\"},\"943\":{\"date\":\"2103-07-31\",\"value\":\"0\"},\"944\":{\"date\":\"2103-08-31\",\"value\":\"0\"},\"945\":{\"date\":\"2103-09-30\",\"value\":\"0\"},\"946\":{\"date\":\"2103-10-31\",\"value\":\"0\"},\"947\":{\"date\":\"2103-11-30\",\"value\":\"0\"},\"948\":{\"date\":\"2103-12-31\",\"value\":\"0\"},\"949\":{\"date\":\"2104-01-31\",\"value\":\"0\"},\"950\":{\"date\":\"2104-02-29\",\"value\":\"0\"},\"951\":{\"date\":\"2104-03-31\",\"value\":\"0\"},\"952\":{\"date\":\"2104-04-30\",\"value\":\"0\"},\"953\":{\"date\":\"2104-05-31\",\"value\":\"0\"},\"954\":{\"date\":\"2104-06-30\",\"value\":\"0\"},\"955\":{\"date\":\"2104-07-31\",\"value\":\"0\"},\"956\":{\"date\":\"2104-08-31\",\"value\":\"0\"},\"957\":{\"date\":\"2104-09-30\",\"value\":\"0\"},\"958\":{\"date\":\"2104-10-31\",\"value\":\"0\"},\"959\":{\"date\":\"2104-11-30\",\"value\":\"0\"},\"960\":{\"date\":\"2104-12-31\",\"value\":\"0\"},\"961\":{\"date\":\"2105-01-31\",\"value\":\"0\"},\"962\":{\"date\":\"2105-02-28\",\"value\":\"0\"},\"963\":{\"date\":\"2105-03-31\",\"value\":\"0\"},\"964\":{\"date\":\"2105-04-30\",\"value\":\"0\"},\"965\":{\"date\":\"2105-05-31\",\"value\":\"0\"},\"966\":{\"date\":\"2105-06-30\",\"value\":\"0\"},\"967\":{\"date\":\"2105-07-31\",\"value\":\"0\"},\"968\":{\"date\":\"2105-08-31\",\"value\":\"0\"},\"969\":{\"date\":\"2105-09-30\",\"value\":\"0\"},\"970\":{\"date\":\"2105-10-31\",\"value\":\"0\"},\"971\":{\"date\":\"2105-11-30\",\"value\":\"0\"},\"972\":{\"date\":\"2105-12-31\",\"value\":\"0\"},\"973\":{\"date\":\"2106-01-31\",\"value\":\"0\"},\"974\":{\"date\":\"2106-02-28\",\"value\":\"0\"},\"975\":{\"date\":\"2106-03-31\",\"value\":\"0\"},\"976\":{\"date\":\"2106-04-30\",\"value\":\"0\"},\"977\":{\"date\":\"2106-05-31\",\"value\":\"0\"},\"978\":{\"date\":\"2106-06-30\",\"value\":\"0\"},\"979\":{\"date\":\"2106-07-31\",\"value\":\"0\"},\"980\":{\"date\":\"2106-08-31\",\"value\":\"0\"},\"981\":{\"date\":\"2106-09-30\",\"value\":\"0\"},\"982\":{\"date\":\"2106-10-31\",\"value\":\"0\"},\"983\":{\"date\":\"2106-11-30\",\"value\":\"0\"},\"984\":{\"date\":\"2106-12-31\",\"value\":\"0\"},\"985\":{\"date\":\"2107-01-31\",\"value\":\"0\"},\"986\":{\"date\":\"2107-02-28\",\"value\":\"0\"},\"987\":{\"date\":\"2107-03-31\",\"value\":\"0\"},\"988\":{\"date\":\"2107-04-30\",\"value\":\"0\"},\"989\":{\"date\":\"2107-05-31\",\"value\":\"0\"},\"990\":{\"date\":\"2107-06-30\",\"value\":\"0\"},\"991\":{\"date\":\"2107-07-31\",\"value\":\"0\"},\"992\":{\"date\":\"2107-08-31\",\"value\":\"0\"},\"993\":{\"date\":\"2107-09-30\",\"value\":\"0\"},\"994\":{\"date\":\"2107-10-31\",\"value\":\"0\"},\"995\":{\"date\":\"2107-11-30\",\"value\":\"0\"},\"996\":{\"date\":\"2107-12-31\",\"value\":\"0\"},\"997\":{\"date\":\"2108-01-31\",\"value\":\"0\"},\"998\":{\"date\":\"2108-02-29\",\"value\":\"0\"},\"999\":{\"date\":\"2108-03-31\",\"value\":\"0\"},\"1000\":{\"date\":\"2108-04-30\",\"value\":\"0\"},\"1001\":{\"date\":\"2108-05-31\",\"value\":\"0\"},\"1002\":{\"date\":\"2108-06-30\",\"value\":\"0\"},\"1003\":{\"date\":\"2108-07-31\",\"value\":\"0\"},\"1004\":{\"date\":\"2108-08-31\",\"value\":\"0\"},\"1005\":{\"date\":\"2108-09-30\",\"value\":\"0\"},\"1006\":{\"date\":\"2108-10-31\",\"value\":\"0\"},\"1007\":{\"date\":\"2108-11-30\",\"value\":\"0\"},\"1008\":{\"date\":\"2108-12-31\",\"value\":\"0\"},\"1009\":{\"date\":\"2109-01-31\",\"value\":\"0\"},\"1010\":{\"date\":\"2109-02-28\",\"value\":\"0\"},\"1011\":{\"date\":\"2109-03-31\",\"value\":\"0\"},\"1012\":{\"date\":\"2109-04-30\",\"value\":\"0\"},\"1013\":{\"date\":\"2109-05-31\",\"value\":\"0\"},\"1014\":{\"date\":\"2109-06-30\",\"value\":\"0\"},\"1015\":{\"date\":\"2109-07-31\",\"value\":\"0\"},\"1016\":{\"date\":\"2109-08-31\",\"value\":\"0\"},\"1017\":{\"date\":\"2109-09-30\",\"value\":\"0\"},\"1018\":{\"date\":\"2109-10-31\",\"value\":\"0\"},\"1019\":{\"date\":\"2109-11-30\",\"value\":\"0\"},\"1020\":{\"date\":\"2109-12-31\",\"value\":\"0\"},\"1021\":{\"date\":\"2110-01-31\",\"value\":\"0\"},\"1022\":{\"date\":\"2110-02-28\",\"value\":\"0\"},\"1023\":{\"date\":\"2110-03-31\",\"value\":\"0\"},\"1024\":{\"date\":\"2110-04-30\",\"value\":\"0\"},\"1025\":{\"date\":\"2110-05-31\",\"value\":\"0\"},\"1026\":{\"date\":\"2110-06-30\",\"value\":\"0\"},\"1027\":{\"date\":\"2110-07-31\",\"value\":\"0\"},\"1028\":{\"date\":\"2110-08-31\",\"value\":\"0\"},\"1029\":{\"date\":\"2110-09-30\",\"value\":\"0\"},\"1030\":{\"date\":\"2110-10-31\",\"value\":\"0\"},\"1031\":{\"date\":\"2110-11-30\",\"value\":\"0\"},\"1032\":{\"date\":\"2110-12-31\",\"value\":\"0\"},\"1033\":{\"date\":\"2111-01-31\",\"value\":\"0\"},\"1034\":{\"date\":\"2111-02-28\",\"value\":\"0\"},\"1035\":{\"date\":\"2111-03-31\",\"value\":\"0\"},\"1036\":{\"date\":\"2111-04-30\",\"value\":\"0\"},\"1037\":{\"date\":\"2111-05-31\",\"value\":\"0\"},\"1038\":{\"date\":\"2111-06-30\",\"value\":\"0\"},\"1039\":{\"date\":\"2111-07-31\",\"value\":\"0\"},\"1040\":{\"date\":\"2111-08-31\",\"value\":\"0\"},\"1041\":{\"date\":\"2111-09-30\",\"value\":\"0\"},\"1042\":{\"date\":\"2111-10-31\",\"value\":\"0\"},\"1043\":{\"date\":\"2111-11-30\",\"value\":\"0\"},\"1044\":{\"date\":\"2111-12-31\",\"value\":\"0\"},\"1045\":{\"date\":\"2112-01-31\",\"value\":\"0\"},\"1046\":{\"date\":\"2112-02-29\",\"value\":\"0\"},\"1047\":{\"date\":\"2112-03-31\",\"value\":\"0\"},\"1048\":{\"date\":\"2112-04-30\",\"value\":\"0\"},\"1049\":{\"date\":\"2112-05-31\",\"value\":\"0\"},\"1050\":{\"date\":\"2112-06-30\",\"value\":\"0\"},\"1051\":{\"date\":\"2112-07-31\",\"value\":\"0\"},\"1052\":{\"date\":\"2112-08-31\",\"value\":\"0\"},\"1053\":{\"date\":\"2112-09-30\",\"value\":\"0\"},\"1054\":{\"date\":\"2112-10-31\",\"value\":\"0\"},\"1055\":{\"date\":\"2112-11-30\",\"value\":\"0\"},\"1056\":{\"date\":\"2112-12-31\",\"value\":\"0\"},\"1057\":{\"date\":\"2113-01-31\",\"value\":\"0\"},\"1058\":{\"date\":\"2113-02-28\",\"value\":\"0\"},\"1059\":{\"date\":\"2113-03-31\",\"value\":\"0\"},\"1060\":{\"date\":\"2113-04-30\",\"value\":\"0\"},\"1061\":{\"date\":\"2113-05-31\",\"value\":\"0\"},\"1062\":{\"date\":\"2113-06-30\",\"value\":\"0\"},\"1063\":{\"date\":\"2113-07-31\",\"value\":\"0\"},\"1064\":{\"date\":\"2113-08-31\",\"value\":\"0\"},\"1065\":{\"date\":\"2113-09-30\",\"value\":\"0\"},\"1066\":{\"date\":\"2113-10-31\",\"value\":\"0\"},\"1067\":{\"date\":\"2113-11-30\",\"value\":\"0\"},\"1068\":{\"date\":\"2113-12-31\",\"value\":\"0\"},\"1069\":{\"date\":\"2114-01-31\",\"value\":\"0\"},\"1070\":{\"date\":\"2114-02-28\",\"value\":\"0\"},\"1071\":{\"date\":\"2114-03-31\",\"value\":\"0\"},\"1072\":{\"date\":\"2114-04-30\",\"value\":\"0\"},\"1073\":{\"date\":\"2114-05-31\",\"value\":\"0\"},\"1074\":{\"date\":\"2114-06-30\",\"value\":\"0\"},\"1075\":{\"date\":\"2114-07-31\",\"value\":\"0\"},\"1076\":{\"date\":\"2114-08-31\",\"value\":\"0\"},\"1077\":{\"date\":\"2114-09-30\",\"value\":\"0\"},\"1078\":{\"date\":\"2114-10-31\",\"value\":\"0\"},\"1079\":{\"date\":\"2114-11-30\",\"value\":\"0\"},\"1080\":{\"date\":\"2114-12-31\",\"value\":\"0\"},\"1081\":{\"date\":\"2115-01-31\",\"value\":\"0\"},\"1082\":{\"date\":\"2115-02-28\",\"value\":\"0\"},\"1083\":{\"date\":\"2115-03-31\",\"value\":\"0\"},\"1084\":{\"date\":\"2115-04-30\",\"value\":\"0\"},\"1085\":{\"date\":\"2115-05-31\",\"value\":\"0\"},\"1086\":{\"date\":\"2115-06-30\",\"value\":\"0\"},\"1087\":{\"date\":\"2115-07-31\",\"value\":\"0\"},\"1088\":{\"date\":\"2115-08-31\",\"value\":\"0\"},\"1089\":{\"date\":\"2115-09-30\",\"value\":\"0\"},\"1090\":{\"date\":\"2115-10-31\",\"value\":\"0\"},\"1091\":{\"date\":\"2115-11-30\",\"value\":\"0\"},\"1092\":{\"date\":\"2115-12-31\",\"value\":\"0\"},\"1093\":{\"date\":\"2116-01-31\",\"value\":\"0\"},\"1094\":{\"date\":\"2116-02-29\",\"value\":\"0\"},\"1095\":{\"date\":\"2116-03-31\",\"value\":\"0\"},\"1096\":{\"date\":\"2116-04-30\",\"value\":\"0\"},\"1097\":{\"date\":\"2116-05-31\",\"value\":\"0\"},\"1098\":{\"date\":\"2116-06-30\",\"value\":\"0\"},\"1099\":{\"date\":\"2116-07-31\",\"value\":\"0\"},\"1100\":{\"date\":\"2116-08-31\",\"value\":\"0\"},\"1101\":{\"date\":\"2116-09-30\",\"value\":\"0\"},\"1102\":{\"date\":\"2116-10-31\",\"value\":\"0\"},\"1103\":{\"date\":\"2116-11-30\",\"value\":\"0\"},\"1104\":{\"date\":\"2116-12-31\",\"value\":\"0\"},\"1105\":{\"date\":\"2117-01-31\",\"value\":\"0\"},\"1106\":{\"date\":\"2117-02-28\",\"value\":\"0\"},\"1107\":{\"date\":\"2117-03-31\",\"value\":\"0\"},\"1108\":{\"date\":\"2117-04-30\",\"value\":\"0\"},\"1109\":{\"date\":\"2117-05-31\",\"value\":\"0\"},\"1110\":{\"date\":\"2117-06-30\",\"value\":\"0\"},\"1111\":{\"date\":\"2117-07-31\",\"value\":\"0\"},\"1112\":{\"date\":\"2117-08-31\",\"value\":\"0\"},\"1113\":{\"date\":\"2117-09-30\",\"value\":\"0\"},\"1114\":{\"date\":\"2117-10-31\",\"value\":\"0\"},\"1115\":{\"date\":\"2117-11-30\",\"value\":\"0\"},\"1116\":{\"date\":\"2117-12-31\",\"value\":\"0\"},\"1117\":{\"date\":\"2118-01-31\",\"value\":\"0\"},\"1118\":{\"date\":\"2118-02-28\",\"value\":\"0\"},\"1119\":{\"date\":\"2118-03-31\",\"value\":\"0\"},\"1120\":{\"date\":\"2118-04-30\",\"value\":\"0\"},\"1121\":{\"date\":\"2118-05-31\",\"value\":\"0\"},\"1122\":{\"date\":\"2118-06-30\",\"value\":\"0\"},\"1123\":{\"date\":\"2118-07-31\",\"value\":\"0\"},\"1124\":{\"date\":\"2118-08-31\",\"value\":\"0\"},\"1125\":{\"date\":\"2118-09-30\",\"value\":\"0\"},\"1126\":{\"date\":\"2118-10-31\",\"value\":\"0\"},\"1127\":{\"date\":\"2118-11-30\",\"value\":\"0\"},\"1128\":{\"date\":\"2118-12-31\",\"value\":\"0\"},\"1129\":{\"date\":\"2119-01-31\",\"value\":\"0\"},\"1130\":{\"date\":\"2119-02-28\",\"value\":\"0\"},\"1131\":{\"date\":\"2119-03-31\",\"value\":\"0\"},\"1132\":{\"date\":\"2119-04-30\",\"value\":\"0\"},\"1133\":{\"date\":\"2119-05-31\",\"value\":\"0\"},\"1134\":{\"date\":\"2119-06-30\",\"value\":\"0\"},\"1135\":{\"date\":\"2119-07-31\",\"value\":\"0\"},\"1136\":{\"date\":\"2119-08-31\",\"value\":\"0\"},\"1137\":{\"date\":\"2119-09-30\",\"value\":\"0\"},\"1138\":{\"date\":\"2119-10-31\",\"value\":\"0\"},\"1139\":{\"date\":\"2119-11-30\",\"value\":\"0\"},\"1140\":{\"date\":\"2119-12-31\",\"value\":\"0\"},\"1141\":{\"date\":\"2120-01-31\",\"value\":\"0\"},\"1142\":{\"date\":\"2120-02-29\",\"value\":\"0\"},\"1143\":{\"date\":\"2120-03-31\",\"value\":\"0\"},\"1144\":{\"date\":\"2120-04-30\",\"value\":\"0\"},\"1145\":{\"date\":\"2120-05-31\",\"value\":\"0\"},\"1146\":{\"date\":\"2120-06-30\",\"value\":\"0\"},\"1147\":{\"date\":\"2120-07-31\",\"value\":\"0\"},\"1148\":{\"date\":\"2120-08-31\",\"value\":\"0\"},\"1149\":{\"date\":\"2120-09-30\",\"value\":\"0\"},\"1150\":{\"date\":\"2120-10-31\",\"value\":\"0\"},\"1151\":{\"date\":\"2120-11-30\",\"value\":\"0\"},\"1152\":{\"date\":\"2120-12-31\",\"value\":\"0\"},\"1153\":{\"date\":\"2121-01-31\",\"value\":\"0\"},\"1154\":{\"date\":\"2121-02-28\",\"value\":\"0\"},\"1155\":{\"date\":\"2121-03-31\",\"value\":\"0\"},\"1156\":{\"date\":\"2121-04-30\",\"value\":\"0\"},\"1157\":{\"date\":\"2121-05-31\",\"value\":\"0\"},\"1158\":{\"date\":\"2121-06-30\",\"value\":\"0\"},\"1159\":{\"date\":\"2121-07-31\",\"value\":\"0\"},\"1160\":{\"date\":\"2121-08-31\",\"value\":\"0\"},\"1161\":{\"date\":\"2121-09-30\",\"value\":\"0\"},\"1162\":{\"date\":\"2121-10-31\",\"value\":\"0\"},\"1163\":{\"date\":\"2121-11-30\",\"value\":\"0\"},\"1164\":{\"date\":\"2121-12-31\",\"value\":\"0\"},\"1165\":{\"date\":\"2122-01-31\",\"value\":\"0\"},\"1166\":{\"date\":\"2122-02-28\",\"value\":\"0\"},\"1167\":{\"date\":\"2122-03-31\",\"value\":\"0\"},\"1168\":{\"date\":\"2122-04-30\",\"value\":\"0\"},\"1169\":{\"date\":\"2122-05-31\",\"value\":\"0\"},\"1170\":{\"date\":\"2122-06-30\",\"value\":\"0\"},\"1171\":{\"date\":\"2122-07-31\",\"value\":\"0\"},\"1172\":{\"date\":\"2122-08-31\",\"value\":\"0\"},\"1173\":{\"date\":\"2122-09-30\",\"value\":\"0\"},\"1174\":{\"date\":\"2122-10-31\",\"value\":\"0\"},\"1175\":{\"date\":\"2122-11-30\",\"value\":\"0\"},\"1176\":{\"date\":\"2122-12-31\",\"value\":\"0\"},\"1177\":{\"date\":\"2123-01-31\",\"value\":\"0\"},\"1178\":{\"date\":\"2123-02-28\",\"value\":\"0\"},\"1179\":{\"date\":\"2123-03-31\",\"value\":\"0\"},\"1180\":{\"date\":\"2123-04-30\",\"value\":\"0\"},\"1181\":{\"date\":\"2123-05-31\",\"value\":\"0\"},\"1182\":{\"date\":\"2123-06-30\",\"value\":\"0\"},\"1183\":{\"date\":\"2123-07-31\",\"value\":\"0\"},\"1184\":{\"date\":\"2123-08-31\",\"value\":\"0\"},\"1185\":{\"date\":\"2123-09-30\",\"value\":\"0\"},\"1186\":{\"date\":\"2123-10-31\",\"value\":\"0\"},\"1187\":{\"date\":\"2123-11-30\",\"value\":\"0\"},\"1188\":{\"date\":\"2123-12-31\",\"value\":\"0\"},\"1189\":{\"date\":\"2124-01-31\",\"value\":\"0\"},\"1190\":{\"date\":\"2124-02-29\",\"value\":\"0\"},\"1191\":{\"date\":\"2124-03-31\",\"value\":\"0\"},\"1192\":{\"date\":\"2124-04-30\",\"value\":\"0\"},\"1193\":{\"date\":\"2124-05-31\",\"value\":\"0\"},\"1194\":{\"date\":\"2124-06-30\",\"value\":\"0\"},\"1195\":{\"date\":\"2124-07-31\",\"value\":\"0\"},\"1196\":{\"date\":\"2124-08-31\",\"value\":\"0\"},\"1197\":{\"date\":\"2124-09-30\",\"value\":\"0\"},\"1198\":{\"date\":\"2124-10-31\",\"value\":\"0\"},\"1199\":{\"date\":\"2124-11-30\",\"value\":\"0\"},\"1200\":{\"date\":\"2124-12-31\",\"value\":\"0\"},\"1201\":{\"date\":\"2125-01-31\",\"value\":\"0\"},\"1202\":{\"date\":\"2125-02-28\",\"value\":\"0\"},\"1203\":{\"date\":\"2125-03-31\",\"value\":\"0\"},\"1204\":{\"date\":\"2125-04-30\",\"value\":\"0\"},\"1205\":{\"date\":\"2125-05-31\",\"value\":\"0\"},\"1206\":{\"date\":\"2125-06-30\",\"value\":\"0\"},\"1207\":{\"date\":\"2125-07-31\",\"value\":\"0\"},\"1208\":{\"date\":\"2125-08-31\",\"value\":\"0\"},\"1209\":{\"date\":\"2125-09-30\",\"value\":\"0\"},\"1210\":{\"date\":\"2125-10-31\",\"value\":\"0\"},\"1211\":{\"date\":\"2125-11-30\",\"value\":\"0\"},\"1212\":{\"date\":\"2125-12-31\",\"value\":\"0\"},\"1213\":{\"date\":\"2126-01-31\",\"value\":\"0\"},\"1214\":{\"date\":\"2126-02-28\",\"value\":\"0\"},\"1215\":{\"date\":\"2126-03-31\",\"value\":\"0\"},\"1216\":{\"date\":\"2126-04-30\",\"value\":\"0\"},\"1217\":{\"date\":\"2126-05-31\",\"value\":\"0\"},\"1218\":{\"date\":\"2126-06-30\",\"value\":\"0\"},\"1219\":{\"date\":\"2126-07-31\",\"value\":\"0\"},\"1220\":{\"date\":\"2126-08-31\",\"value\":\"0\"},\"1221\":{\"date\":\"2126-09-30\",\"value\":\"0\"},\"1222\":{\"date\":\"2126-10-31\",\"value\":\"0\"},\"1223\":{\"date\":\"2126-11-30\",\"value\":\"0\"},\"1224\":{\"date\":\"2126-12-31\",\"value\":\"0\"},\"1225\":{\"date\":\"2127-01-31\",\"value\":\"0\"},\"1226\":{\"date\":\"2127-02-28\",\"value\":\"0\"},\"1227\":{\"date\":\"2127-03-31\",\"value\":\"0\"},\"1228\":{\"date\":\"2127-04-30\",\"value\":\"0\"},\"1229\":{\"date\":\"2127-05-31\",\"value\":\"0\"},\"1230\":{\"date\":\"2127-06-30\",\"value\":\"0\"},\"1231\":{\"date\":\"2127-07-31\",\"value\":\"0\"},\"1232\":{\"date\":\"2127-08-31\",\"value\":\"0\"},\"1233\":{\"date\":\"2127-09-30\",\"value\":\"0\"},\"1234\":{\"date\":\"2127-10-31\",\"value\":\"0\"},\"1235\":{\"date\":\"2127-11-30\",\"value\":\"0\"},\"1236\":{\"date\":\"2127-12-31\",\"value\":\"0\"},\"1237\":{\"date\":\"2128-01-31\",\"value\":\"0\"},\"1238\":{\"date\":\"2128-02-29\",\"value\":\"0\"},\"1239\":{\"date\":\"2128-03-31\",\"value\":\"0\"},\"1240\":{\"date\":\"2128-04-30\",\"value\":\"0\"},\"1241\":{\"date\":\"2128-05-31\",\"value\":\"0\"},\"1242\":{\"date\":\"2128-06-30\",\"value\":\"0\"},\"1243\":{\"date\":\"2128-07-31\",\"value\":\"0\"},\"1244\":{\"date\":\"2128-08-31\",\"value\":\"0\"},\"1245\":{\"date\":\"2128-09-30\",\"value\":\"0\"},\"1246\":{\"date\":\"2128-10-31\",\"value\":\"0\"},\"1247\":{\"date\":\"2128-11-30\",\"value\":\"0\"},\"1248\":{\"date\":\"2128-12-31\",\"value\":\"0\"},\"1249\":{\"date\":\"2129-01-31\",\"value\":\"0\"},\"1250\":{\"date\":\"2129-02-28\",\"value\":\"0\"},\"1251\":{\"date\":\"2129-03-31\",\"value\":\"0\"},\"1252\":{\"date\":\"2129-04-30\",\"value\":\"0\"},\"1253\":{\"date\":\"2129-05-31\",\"value\":\"0\"},\"1254\":{\"date\":\"2129-06-30\",\"value\":\"0\"},\"1255\":{\"date\":\"2129-07-31\",\"value\":\"0\"},\"1256\":{\"date\":\"2129-08-31\",\"value\":\"0\"},\"1257\":{\"date\":\"2129-09-30\",\"value\":\"0\"},\"1258\":{\"date\":\"2129-10-31\",\"value\":\"0\"},\"1259\":{\"date\":\"2129-11-30\",\"value\":\"0\"},\"1260\":{\"date\":\"2129-12-31\",\"value\":\"0\"},\"1261\":{\"date\":\"2130-01-31\",\"value\":\"0\"},\"1262\":{\"date\":\"2130-02-28\",\"value\":\"0\"},\"1263\":{\"date\":\"2130-03-31\",\"value\":\"0\"},\"1264\":{\"date\":\"2130-04-30\",\"value\":\"0\"},\"1265\":{\"date\":\"2130-05-31\",\"value\":\"0\"},\"1266\":{\"date\":\"2130-06-30\",\"value\":\"0\"},\"1267\":{\"date\":\"2130-07-31\",\"value\":\"0\"},\"1268\":{\"date\":\"2130-08-31\",\"value\":\"0\"},\"1269\":{\"date\":\"2130-09-30\",\"value\":\"0\"},\"1270\":{\"date\":\"2130-10-31\",\"value\":\"0\"},\"1271\":{\"date\":\"2130-11-30\",\"value\":\"0\"},\"1272\":{\"date\":\"2130-12-31\",\"value\":\"0\"}}";

        // 从JSON中提取日期和值
        Map<String, Object> extractedData = extractDataFromJson(json);
        Date[] days;
        double[] payments;

        if (extractedData != null) {
            days = (Date[]) extractedData.get("days");
            payments = (double[]) extractedData.get("payments");

            // 打印从JSON提取的数据
            System.out.println("从JSON提取的数据:");
            System.out.println("日期数组长度: " + days.length);
            System.out.println("支付数组长度: " + payments.length);
            for (int i = 0; i < days.length; i++) {
                System.out.println("第" + i + "项: 日期=" + sdf.format(days[i]) + ", 值=" + payments[i]);
            }
        } else {
            // 如果JSON解析失败，使用原来的硬编码数据作为备用
            payments = new double[]{14757730,-434986,-423249,-411824,-400705,-389887,-379359,-369218,-359465,-349968,-340507,-331321,-322370,-313787,-305298,-297037,-288997,-281176,-273565,-266247,-259225,-252386,-245547,-238910,-232441,-226250,-220116,-214146,-208336,-252202,-327150,-958024,-1659816,-1780513,-55281,-319611,-203828,-1284650};
            days = new Date[]{strToDate("2024-12-31"),strToDate("2025-1-31"),strToDate("2025-2-28"),strToDate("2025-3-31"),strToDate("2025-4-30"),strToDate("2025-5-31"),strToDate("2025-6-30"),strToDate("2025-7-31"),strToDate("2025-8-31"),strToDate("2025-9-30"),strToDate("2025-10-31"),strToDate("2025-11-30"),strToDate("2025-12-31"),strToDate("2026-1-31"),strToDate("2026-2-28"),strToDate("2026-3-31"),strToDate("2026-4-30"),strToDate("2026-5-31"),strToDate("2026-6-30"),strToDate("2026-7-31"),strToDate("2026-8-31"),strToDate("2026-9-30"),strToDate("2026-10-31"),strToDate("2026-11-30"),strToDate("2026-12-31"),strToDate("2027-1-31"),strToDate("2027-2-28"),strToDate("2027-3-31"),strToDate("2027-4-30"),strToDate("2027-5-31"),strToDate("2027-6-30"),strToDate("2027-7-31"),strToDate("2027-8-31"),strToDate("2027-9-30"),strToDate("2027-10-31"),strToDate("2027-11-30"),strToDate("2027-12-31"),strToDate("2028-1-31")};
        }
        System.out.println(payments.length);
        System.out.println(days.length);
        BigDecimal xirr = Newtons_method(new BigDecimal("0"), payments, days);

//        System.out.println("XIRR value is " + xirr.multiply(new BigDecimal("100")).setScale(8, RoundingMode.HALF_UP));
        System.out.println("XIRR value is " + xirr.setScale(15, RoundingMode.HALF_UP));
    }
}
