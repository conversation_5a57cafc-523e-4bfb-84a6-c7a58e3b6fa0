package com.xl.alm.job.common.util;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

public class XirrCalculator {
    public static final BigDecimal TOL = new BigDecimal("0.001");
    private static final MathContext MC = new MathContext(15, RoundingMode.HALF_UP);
//    private static final BigDecimal DAYS_IN_YEAR = new BigDecimal("365.0006576");
    private static final BigDecimal DAYS_IN_YEAR = new BigDecimal("365.0");

    public static BigDecimal dateDiff(Date d1, Date d2) {
        long day = 24 * 60 * 60 * 1000;
        long diff = d1.getTime() - d2.getTime();
        return new BigDecimal(diff).divide(new BigDecimal(day), MC);
    }

    public static BigDecimal f_xirr(BigDecimal p, Date dt, Date dt0, BigDecimal x) {
        BigDecimal onePlusX = BigDecimal.ONE.add(x);
        BigDecimal dayRatio = dateDiff(dt0, dt).divide(DAYS_IN_YEAR, MC);
        return p.multiply(pow(onePlusX, dayRatio, MC));
    }

    // 重载方法，支持double类型参数
    public static BigDecimal f_xirr(double p, Date dt, Date dt0, double x) {
        return f_xirr(new BigDecimal(p), dt, dt0, new BigDecimal(x));
    }

    public static BigDecimal df_xirr(BigDecimal p, Date dt, Date dt0, BigDecimal x) {
        BigDecimal dayRatio = dateDiff(dt0, dt).divide(DAYS_IN_YEAR, MC);
        BigDecimal onePlusX = BigDecimal.ONE.add(x);
        BigDecimal exponent = dayRatio.subtract(BigDecimal.ONE);

        return BigDecimal.ONE.divide(DAYS_IN_YEAR, MC)
                .multiply(dateDiff(dt0, dt))
                .multiply(p)
                .multiply(pow(onePlusX, exponent, MC));
    }

    // 重载方法，支持double类型参数
    public static BigDecimal df_xirr(double p, Date dt, Date dt0, double x) {
        return df_xirr(new BigDecimal(p), dt, dt0, new BigDecimal(x));
    }

    public static BigDecimal total_f_xirr(double[] payments, Date[] days, BigDecimal x) {
        BigDecimal resf = BigDecimal.ZERO;

        for (int i = 0; i < payments.length; i++) {
            resf = resf.add(f_xirr(new BigDecimal(payments[i]), days[i], days[0], x));
        }

        return resf;
    }

    // 重载方法，支持double类型参数
    public static BigDecimal total_f_xirr(double[] payments, Date[] days, double x) {
        return total_f_xirr(payments, days, new BigDecimal(x));
    }

    public static BigDecimal total_df_xirr(double[] payments, Date[] days, BigDecimal x) {
        BigDecimal resf = BigDecimal.ZERO;

        for (int i = 0; i < payments.length; i++) {
            resf = resf.add(df_xirr(new BigDecimal(payments[i]), days[i], days[0], x));
        }

        return resf;
    }

    // 重载方法，支持double类型参数
    public static BigDecimal total_df_xirr(double[] payments, Date[] days, double x) {
        return total_df_xirr(payments, days, new BigDecimal(x));
    }

    public static BigDecimal Newtons_method(BigDecimal guess, double[] payments, Date[] days) {
        BigDecimal x0 = guess;
        BigDecimal x1;
        BigDecimal err = new BigDecimal("1e+100");

        while (err.compareTo(TOL) > 0) {
            BigDecimal totalF = total_f_xirr(payments, days, x0);
            BigDecimal totalDf = total_df_xirr(payments, days, x0);
            x1 = x0.subtract(totalF.divide(totalDf, MC));
            err = x1.subtract(x0).abs();
            x0 = x1;
        }

        return x0;
    }

    // 重载方法，支持double类型参数
    public static BigDecimal Newtons_method(double guess, double[] payments, Date[] days) {
        return Newtons_method(new BigDecimal(guess), payments, days);
    }

    /**
     * 计算 base 的 exponent 次幂，支持小数指数
     * @param base 底数
     * @param exponent 指数
     * @param mc 精度控制
     * @return base^exponent
     */
    private static BigDecimal pow(BigDecimal base, BigDecimal exponent, MathContext mc) {
        // 如果指数是整数，直接使用 BigDecimal 的 pow 方法
        if (exponent.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
            return base.pow(exponent.intValue(), mc);
        }

        // 对于小数指数，使用自然对数计算: base^exponent = e^(exponent * ln(base))
        BigDecimal lnBase = new BigDecimal(Math.log(base.doubleValue()));
        BigDecimal result = new BigDecimal(Math.exp(exponent.multiply(lnBase).doubleValue()));
        return result.round(mc);
    }

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    public static Date strToDate(String str) {
        try {
            return sdf.parse(str);
        } catch (ParseException ex) {
            return null;
        }
    }

    /**
     * 从JSON字符串中提取日期和值数组
     * @param jsonStr JSON字符串
     * @return 包含days和payments的Map
     */
    public static Map<String, Object> extractDataFromJson(String jsonStr) {
        try {
            JSONObject jsonObject = JSON.parseObject(jsonStr);

            // 创建列表来存储数据
            List<Date> dateList = new ArrayList<>();
            List<Double> paymentList = new ArrayList<>();

            // 按照键的数字顺序排序
            List<String> sortedKeys = new ArrayList<>(jsonObject.keySet());
            sortedKeys.sort((a, b) -> {
                try {
                    return Integer.compare(Integer.parseInt(a), Integer.parseInt(b));
                } catch (NumberFormatException e) {
                    return a.compareTo(b);
                }
            });

            // 遍历排序后的键
            for (String key : sortedKeys) {
                JSONObject entry = jsonObject.getJSONObject(key);
                String dateStr = entry.getString("date");
                String valueStr = entry.getString("value");

                Date date = strToDate(dateStr);
                if (date != null) {
                    dateList.add(date);
                    paymentList.add(Double.parseDouble(valueStr));
                }
            }

            // 转换为数组
            Date[] days = dateList.toArray(new Date[0]);
            double[] payments = paymentList.stream().mapToDouble(Double::doubleValue).toArray();

            Map<String, Object> result = new HashMap<>();
            result.put("days", days);
            result.put("payments", payments);

            return result;
        } catch (Exception e) {
            System.err.println("解析JSON时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        String json="{\\\"0\\\":{\\\"date\\\":\\\"2024-12-31\\\",\\\"value\\\":\\\"268268.8000000000\\\"},\\\"1\\\":{\\\"date\\\":\\\"2025-01-31\\\",\\\"value\\\":\\\"-7904.92317432\\\"}";
        double[] payments = {14757730,-434986,-423249,-411824,-400705,-389887,-379359,-369218,-359465,-349968,-340507,-331321,-322370,-313787,-305298,-297037,-288997,-281176,-273565,-266247,-259225,-252386,-245547,-238910,-232441,-226250,-220116,-214146,-208336,-252202,-327150,-958024,-1659816,-1780513,-55281,-319611,-203828,-1284650};
        Date[] days = {strToDate("2024-12-31"),strToDate("2025-1-31"),strToDate("2025-2-28"),strToDate("2025-3-31"),strToDate("2025-4-30"),strToDate("2025-5-31"),strToDate("2025-6-30"),strToDate("2025-7-31"),strToDate("2025-8-31"),strToDate("2025-9-30"),strToDate("2025-10-31"),strToDate("2025-11-30"),strToDate("2025-12-31"),strToDate("2026-1-31"),strToDate("2026-2-28"),strToDate("2026-3-31"),strToDate("2026-4-30"),strToDate("2026-5-31"),strToDate("2026-6-30"),strToDate("2026-7-31"),strToDate("2026-8-31"),strToDate("2026-9-30"),strToDate("2026-10-31"),strToDate("2026-11-30"),strToDate("2026-12-31"),strToDate("2027-1-31"),strToDate("2027-2-28"),strToDate("2027-3-31"),strToDate("2027-4-30"),strToDate("2027-5-31"),strToDate("2027-6-30"),strToDate("2027-7-31"),strToDate("2027-8-31"),strToDate("2027-9-30"),strToDate("2027-10-31"),strToDate("2027-11-30"),strToDate("2027-12-31"),strToDate("2028-1-31")};
        System.out.println(payments.length);
        System.out.println(days.length);
        BigDecimal xirr = Newtons_method(new BigDecimal("0"), payments, days);

//        System.out.println("XIRR value is " + xirr.multiply(new BigDecimal("100")).setScale(8, RoundingMode.HALF_UP));
        System.out.println("XIRR value is " + xirr.setScale(15, RoundingMode.HALF_UP));
    }
}
